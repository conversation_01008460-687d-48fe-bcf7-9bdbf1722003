# Use the official Bun image
FROM oven/bun:1

# Set working directory
WORKDIR /usr/src/app

# Copy package files
COPY package.json bun.lockb* ./

# Install all dependencies (including devDependencies for build)
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Set production environment
ENV NODE_ENV=production

# Build the application
RUN bun run build

# Expose port
EXPOSE 3000

# Run the app
CMD ["bun", "run", ".output/server/index.mjs"]