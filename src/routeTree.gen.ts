/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthedRouteRouteImport } from './routes/_authed/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthedAdminRouteRouteImport } from './routes/_authed/admin/route'
import { Route as AuthedAdminIndexRouteImport } from './routes/_authed/admin/index'
import { Route as AuthedAdminWorkersIndexRouteImport } from './routes/_authed/admin/workers/index'
import { Route as AuthedAdminSessionsIndexRouteImport } from './routes/_authed/admin/sessions/index'
import { Route as AuthedAdminSchedulesIndexRouteImport } from './routes/_authed/admin/schedules/index'
import { Route as AuthedAdminClientsIndexRouteImport } from './routes/_authed/admin/clients/index'
import { Route as AuthedAdminSchedulesCreateRouteImport } from './routes/_authed/admin/schedules/create'
import { Route as AuthedAdminSchedulesEditIdRouteImport } from './routes/_authed/admin/schedules/edit/$id'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedRouteRoute = AuthedRouteRouteImport.update({
  id: '/_authed',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedAdminRouteRoute = AuthedAdminRouteRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthedRouteRoute,
} as any)
const AuthedAdminIndexRoute = AuthedAdminIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)
const AuthedAdminWorkersIndexRoute = AuthedAdminWorkersIndexRouteImport.update({
  id: '/workers/',
  path: '/workers/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)
const AuthedAdminSessionsIndexRoute =
  AuthedAdminSessionsIndexRouteImport.update({
    id: '/sessions/',
    path: '/sessions/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminSchedulesIndexRoute =
  AuthedAdminSchedulesIndexRouteImport.update({
    id: '/schedules/',
    path: '/schedules/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminClientsIndexRoute = AuthedAdminClientsIndexRouteImport.update({
  id: '/clients/',
  path: '/clients/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)
const AuthedAdminSchedulesCreateRoute =
  AuthedAdminSchedulesCreateRouteImport.update({
    id: '/schedules/create',
    path: '/schedules/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminSchedulesEditIdRoute =
  AuthedAdminSchedulesEditIdRouteImport.update({
    id: '/schedules/edit/$id',
    path: '/schedules/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminRouteRouteWithChildren
  '/admin/': typeof AuthedAdminIndexRoute
  '/admin/schedules/create': typeof AuthedAdminSchedulesCreateRoute
  '/admin/clients': typeof AuthedAdminClientsIndexRoute
  '/admin/schedules': typeof AuthedAdminSchedulesIndexRoute
  '/admin/sessions': typeof AuthedAdminSessionsIndexRoute
  '/admin/workers': typeof AuthedAdminWorkersIndexRoute
  '/admin/schedules/edit/$id': typeof AuthedAdminSchedulesEditIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminIndexRoute
  '/admin/schedules/create': typeof AuthedAdminSchedulesCreateRoute
  '/admin/clients': typeof AuthedAdminClientsIndexRoute
  '/admin/schedules': typeof AuthedAdminSchedulesIndexRoute
  '/admin/sessions': typeof AuthedAdminSessionsIndexRoute
  '/admin/workers': typeof AuthedAdminWorkersIndexRoute
  '/admin/schedules/edit/$id': typeof AuthedAdminSchedulesEditIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authed': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/_authed/admin': typeof AuthedAdminRouteRouteWithChildren
  '/_authed/admin/': typeof AuthedAdminIndexRoute
  '/_authed/admin/schedules/create': typeof AuthedAdminSchedulesCreateRoute
  '/_authed/admin/clients/': typeof AuthedAdminClientsIndexRoute
  '/_authed/admin/schedules/': typeof AuthedAdminSchedulesIndexRoute
  '/_authed/admin/sessions/': typeof AuthedAdminSessionsIndexRoute
  '/_authed/admin/workers/': typeof AuthedAdminWorkersIndexRoute
  '/_authed/admin/schedules/edit/$id': typeof AuthedAdminSchedulesEditIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/'
    | '/admin/schedules/create'
    | '/admin/clients'
    | '/admin/schedules'
    | '/admin/sessions'
    | '/admin/workers'
    | '/admin/schedules/edit/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/schedules/create'
    | '/admin/clients'
    | '/admin/schedules'
    | '/admin/sessions'
    | '/admin/workers'
    | '/admin/schedules/edit/$id'
  id:
    | '__root__'
    | '/'
    | '/_authed'
    | '/login'
    | '/_authed/admin'
    | '/_authed/admin/'
    | '/_authed/admin/schedules/create'
    | '/_authed/admin/clients/'
    | '/_authed/admin/schedules/'
    | '/_authed/admin/sessions/'
    | '/_authed/admin/workers/'
    | '/_authed/admin/schedules/edit/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthedRouteRoute: typeof AuthedRouteRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed': {
      id: '/_authed'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthedRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/admin': {
      id: '/_authed/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthedAdminRouteRouteImport
      parentRoute: typeof AuthedRouteRoute
    }
    '/_authed/admin/': {
      id: '/_authed/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AuthedAdminIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/workers/': {
      id: '/_authed/admin/workers/'
      path: '/workers'
      fullPath: '/admin/workers'
      preLoaderRoute: typeof AuthedAdminWorkersIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/sessions/': {
      id: '/_authed/admin/sessions/'
      path: '/sessions'
      fullPath: '/admin/sessions'
      preLoaderRoute: typeof AuthedAdminSessionsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/schedules/': {
      id: '/_authed/admin/schedules/'
      path: '/schedules'
      fullPath: '/admin/schedules'
      preLoaderRoute: typeof AuthedAdminSchedulesIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/clients/': {
      id: '/_authed/admin/clients/'
      path: '/clients'
      fullPath: '/admin/clients'
      preLoaderRoute: typeof AuthedAdminClientsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/schedules/create': {
      id: '/_authed/admin/schedules/create'
      path: '/schedules/create'
      fullPath: '/admin/schedules/create'
      preLoaderRoute: typeof AuthedAdminSchedulesCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/schedules/edit/$id': {
      id: '/_authed/admin/schedules/edit/$id'
      path: '/schedules/edit/$id'
      fullPath: '/admin/schedules/edit/$id'
      preLoaderRoute: typeof AuthedAdminSchedulesEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
  }
}

interface AuthedAdminRouteRouteChildren {
  AuthedAdminIndexRoute: typeof AuthedAdminIndexRoute
  AuthedAdminSchedulesCreateRoute: typeof AuthedAdminSchedulesCreateRoute
  AuthedAdminClientsIndexRoute: typeof AuthedAdminClientsIndexRoute
  AuthedAdminSchedulesIndexRoute: typeof AuthedAdminSchedulesIndexRoute
  AuthedAdminSessionsIndexRoute: typeof AuthedAdminSessionsIndexRoute
  AuthedAdminWorkersIndexRoute: typeof AuthedAdminWorkersIndexRoute
  AuthedAdminSchedulesEditIdRoute: typeof AuthedAdminSchedulesEditIdRoute
}

const AuthedAdminRouteRouteChildren: AuthedAdminRouteRouteChildren = {
  AuthedAdminIndexRoute: AuthedAdminIndexRoute,
  AuthedAdminSchedulesCreateRoute: AuthedAdminSchedulesCreateRoute,
  AuthedAdminClientsIndexRoute: AuthedAdminClientsIndexRoute,
  AuthedAdminSchedulesIndexRoute: AuthedAdminSchedulesIndexRoute,
  AuthedAdminSessionsIndexRoute: AuthedAdminSessionsIndexRoute,
  AuthedAdminWorkersIndexRoute: AuthedAdminWorkersIndexRoute,
  AuthedAdminSchedulesEditIdRoute: AuthedAdminSchedulesEditIdRoute,
}

const AuthedAdminRouteRouteWithChildren =
  AuthedAdminRouteRoute._addFileChildren(AuthedAdminRouteRouteChildren)

interface AuthedRouteRouteChildren {
  AuthedAdminRouteRoute: typeof AuthedAdminRouteRouteWithChildren
}

const AuthedRouteRouteChildren: AuthedRouteRouteChildren = {
  AuthedAdminRouteRoute: AuthedAdminRouteRouteWithChildren,
}

const AuthedRouteRouteWithChildren = AuthedRouteRoute._addFileChildren(
  AuthedRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthedRouteRoute: AuthedRouteRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
