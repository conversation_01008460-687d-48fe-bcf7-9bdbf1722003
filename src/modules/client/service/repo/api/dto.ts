import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Create<PERSON>erson<PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Person<PERSON>rom<PERSON><PERSON>,
	UpdatePersonApi,
	UpdatePersonApiFromUpdatePerson,
} from "~/person/service/repo/api/dto";
import { Client, CreateClient, UpdateClient } from "../../model/client";
import {
	ClientLinkResponse,
	CreatePublicClientLink,
	PublicClientLink,
	SessionInfo,
	UpdatePublicClientLink,
} from "../../model/publicClientLink";

export const ClientApi = Schema.Struct({
	id: Schema.String,
	person: PersonApi,
	public_link: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ClientFromApi = Schema.transform(Client<PERSON><PERSON>, Client, {
	strict: true,
	decode: (clientApi) => ({
		...clientApi,
		person: Schema.decodeUnknownSync(Person<PERSON>rom<PERSON>pi)(clientApi.person),
		publicLink: clientApi.public_link,
		createdAt: clientApi.created_at,
		updatedAt: clientApi.updated_at,
		deletedAt: clientApi.deleted_at,
	}),
	encode: (client) => ({
		...client,
		person: Schema.encodeUnknownSync(PersonFromApi)(client.person),
		public_link: client.publicLink,
		created_at: client.createdAt,
		updated_at: client.updatedAt,
		deleted_at: client.deletedAt,
	}),
});

export const ClientListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ClientFromApi))),
	Schema.mutable(Schema.Array(Client)),
	{
		strict: true,
		decode: (clientApiList) => (clientApiList ? clientApiList : []),
		encode: (clientList) => clientList,
	},
);

export const CreateClientApi = Schema.Struct({
	person: CreatePersonApi,
});

export const CreateClientApiFromCreateClient = Schema.transform(
	CreateClient,
	CreateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
				client.person,
			),
		}),
	},
);

export const CreateClientApiResponse = Schema.String;

export const UpdateClientApi = Schema.Struct({
	id: Schema.String,
	person: UpdatePersonApi,
});

export const UpdateClientApiFromUpdateClient = Schema.transform(
	UpdateClient,
	UpdateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				client.person,
			),
		}),
	},
);

// Public Client Link API schemas
export const PublicClientLinkApi = Schema.Struct({
	id: Schema.String,
	schedule_id: Schema.String,
	turn_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const PublicClientLinkFromApi = Schema.transform(
	PublicClientLinkApi,
	PublicClientLink,
	{
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			scheduleId: linkApi.schedule_id,
			turnId: linkApi.turn_id,
			workerIds: linkApi.worker_ids,
			clientId: linkApi.client_id,
			createdAt: linkApi.created_at,
			updatedAt: linkApi.updated_at,
			deletedAt: linkApi.deleted_at,
		}),
		encode: (link) => ({
			...link,
			schedule_id: link.scheduleId,
			turn_id: link.turnId,
			worker_ids: link.workerIds,
			client_id: link.clientId,
			created_at: link.createdAt,
			updated_at: link.updatedAt,
			deleted_at: link.deletedAt,
		}),
	},
);

export const CreatePublicClientLinkApi = Schema.Struct({
	id: Schema.String,
	schedule_id: Schema.String,
	turn_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
});

export const CreatePublicClientLinkApiFromCreatePublicClientLink =
	Schema.transform(CreatePublicClientLink, CreatePublicClientLinkApi, {
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			schedule_id: linkApi.scheduleId,
			turn_id: linkApi.turnId,
			worker_ids: linkApi.workerIds,
			client_id: linkApi.clientId,
		}),
		encode: (link) => ({
			...link,
			scheduleId: link.schedule_id,
			turnId: link.turn_id,
			workerIds: link.worker_ids,
			clientId: link.client_id,
		}),
	});

export const UpdatePublicClientLinkApi = Schema.Struct({
	id: Schema.String,
	schedule_id: Schema.String,
	turn_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
});

export const UpdatePublicClientLinkApiFromUpdatePublicClientLink =
	Schema.transform(UpdatePublicClientLink, UpdatePublicClientLinkApi, {
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			schedule_id: linkApi.scheduleId,
			turn_id: linkApi.turnId,
			worker_ids: linkApi.workerIds,
			client_id: linkApi.clientId,
		}),
		encode: (link) => ({
			...link,
			scheduleId: link.schedule_id,
			turnId: link.turn_id,
			workerIds: link.worker_ids,
			clientId: link.client_id,
		}),
	});

// Session Info API schema
export const SessionInfoApi = Schema.Struct({
	id: Schema.String,
	client_id: Schema.NullOr(Schema.String),
	worker_id: Schema.String,
	turn_id: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const SessionInfoFromApi = Schema.transform(
	SessionInfoApi,
	SessionInfo,
	{
		strict: true,
		decode: (sessionApi) => ({
			...sessionApi,
			clientId: sessionApi.client_id,
			workerId: sessionApi.worker_id,
			turnId: sessionApi.turn_id,
			createdAt: sessionApi.created_at,
			updatedAt: sessionApi.updated_at,
			deletedAt: sessionApi.deleted_at,
		}),
		encode: (session) => ({
			...session,
			client_id: session.clientId,
			worker_id: session.workerId,
			turn_id: session.turnId,
			created_at: session.createdAt,
			updated_at: session.updatedAt,
			deleted_at: session.deletedAt,
		}),
	},
);

// Client Link Response API schema
export const ClientLinkResponseApi = Schema.Struct({
	sessions: Schema.mutable(Schema.Array(SessionInfoApi)),
});

export const ClientLinkResponseFromApi = Schema.transform(
	ClientLinkResponseApi,
	ClientLinkResponse,
	{
		strict: true,
		decode: (responseApi) => ({
			sessions: responseApi.sessions.map((session) =>
				Schema.decodeUnknownSync(SessionInfoFromApi)(session),
			),
		}),
		encode: (response) => ({
			sessions: response.sessions.map((session) =>
				Schema.encodeUnknownSync(SessionInfoFromApi)(session),
			),
		}),
	},
);

export const GeneratePublicLinkResponse = Schema.String;
