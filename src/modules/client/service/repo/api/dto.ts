import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Create<PERSON>ersonApi<PERSON>rom<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Person<PERSON>romApi,
	UpdatePersonApi,
	UpdatePersonApiFromUpdatePerson,
} from "~/person/service/repo/api/dto";
import { CreateClient, UpdateClient, Client } from "../../model/client";

export const ClientApi = Schema.Struct({
	id: Schema.String,
	person: PersonApi,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ClientFromApi = Schema.transform(ClientApi, Client, {
	strict: true,
	decode: (clientApi) => ({
		...clientApi,
		person: Schema.decodeUnknownSync(PersonFromApi)(clientApi.person),
		createdAt: clientApi.created_at,
		updatedAt: clientApi.updated_at,
		deletedAt: clientApi.deleted_at,
	}),
	encode: (client) => ({
		...client,
		person: Schema.encodeUnknownSync(Person<PERSON>rom<PERSON><PERSON>)(client.person),
		created_at: client.createdAt,
		updated_at: client.updatedAt,
		deleted_at: client.deletedAt,
	}),
});

export const ClientListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ClientFromApi))),
	Schema.mutable(Schema.Array(Client)),
	{
		strict: true,
		decode: (clientApiList) => (clientApiList ? clientApiList : []),
		encode: (clientList) => clientList,
	},
);

export const CreateClientApi = Schema.Struct({
	person: CreatePersonApi,
});

export const CreateClientApiFromCreateClient = Schema.transform(
	CreateClient,
	CreateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
				client.person,
			),
		}),
	},
);

export const CreateClientApiResponse = Schema.String;

export const UpdateClientApi = Schema.Struct({
	id: Schema.String,
	person: UpdatePersonApi,
});

export const UpdateClientApiFromUpdateClient = Schema.transform(
	UpdateClient,
	UpdateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				client.person,
			),
		}),
	},
);
