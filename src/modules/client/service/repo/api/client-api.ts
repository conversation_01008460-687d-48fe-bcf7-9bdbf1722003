import { <PERSON>tt<PERSON><PERSON><PERSON> } from "@effect/platform";
import { <PERSON>, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import { ClientRepository } from "../../model/repository";
import type { CreateClient, UpdateClient, Client } from "../../model/client";
import {
	CreateClientApiFromCreateClient,
	CreateClientApiResponse,
	UpdateClientApiFromUpdateClient,
	ClientFromApi,
	ClientListFromApi,
} from "./dto";

const baseUrl = "/v1/clients";

const makeClientApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ClientListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ClientFromApi))),
		create: (client: CreateClient) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateClientApiFromCreateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateClientApiResponse))),
		update: (client: UpdateClient) =>
			httpClient
				.put(`${baseUrl}/${client.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateClientApiFromUpdateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const clientApiRepoLive = Layer.effect(
	ClientRepository,
	makeClientApiRepo,
);
