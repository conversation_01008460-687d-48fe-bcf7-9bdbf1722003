import { Schema } from "effect";

export const PublicClientLink = Schema.Struct({
	id: Schema.String,
	scheduleId: Schema.String,
	turnId: Schema.String,
	workerIds: Schema.mutable(Schema.Array(Schema.String)),
	clientId: Schema.String,
	url: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type PublicClientLink = typeof PublicClientLink.Type;

export const CreatePublicClientLink = Schema.Struct({
	id: Schema.String,
	scheduleId: Schema.String,
	turnId: Schema.String,
	workerIds: Schema.mutable(Schema.Array(Schema.String)),
	clientId: Schema.String,
	url: Schema.String,
});
export type CreatePublicClientLink = typeof CreatePublicClientLink.Type;

export const UpdatePublicClientLink = Schema.Struct({
	id: Schema.String,
	scheduleId: Schema.String,
	turnId: Schema.String,
	workerIds: Schema.mutable(Schema.Array(Schema.String)),
	clientId: Schema.String,
	url: Schema.String,
});
export type UpdatePublicClientLink = typeof UpdatePublicClientLink.Type;

// Session Info for client link response
export const SessionInfo = Schema.Struct({
	id: Schema.String,
	clientId: Schema.NullOr(Schema.String),
	workerId: Schema.String,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type SessionInfo = typeof SessionInfo.Type;

export const ClientLinkResponse = Schema.Struct({
	sessions: Schema.mutable(Schema.Array(SessionInfo)),
});
export type ClientLinkResponse = typeof ClientLinkResponse.Type;
