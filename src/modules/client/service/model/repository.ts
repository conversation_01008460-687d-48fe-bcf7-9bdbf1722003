import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { Client, CreateClient, UpdateClient } from "./client";
import type {
	ClientLinkResponse,
	CreatePublicClientLink,
	UpdatePublicClientLink,
} from "./publicClientLink";

export class ClientRepository extends Effect.Tag("ClientRepository")<
	ClientRepository,
	{
		readonly getAll: () => Effect.Effect<Client[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Client, AppError>;
		readonly create: (client: CreateClient) => Effect.Effect<string, AppError>;
		readonly update: (client: UpdateClient) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly generatePublicLink: () => Effect.Effect<string, AppError>;
		readonly createPublicLink: (
			link: CreatePublicClientLink,
		) => Effect.Effect<void, AppError>;
		readonly updatePublicLink: (
			link: UpdatePublicClientLink,
		) => Effect.Effect<void, AppError>;
		readonly getClientLink: (
			url: string,
		) => Effect.Effect<ClientLinkResponse, AppError>;
		readonly deletePublicLink: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
