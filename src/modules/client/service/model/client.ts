import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	Person,
	UpdatePerson,
} from "~/person/service/model/person";

export const Client = Schema.Struct({
	id: Schema.String,
	person: Person,
	publicLink: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Client = typeof Client.Type;

export const CreateClient = Schema.Struct({
	person: Create<PERSON>erson,
});
export type CreateClient = typeof CreateClient.Type;

export const UpdateClient = Schema.Struct({
	id: Schema.String,
	person: UpdatePerson,
});
export type UpdateClient = typeof UpdateClient.Type;

