import { useQuery } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";

export default function useGetClientLink(url: string, enabled = true) {
	const service = useService();
	const { client } = service;

	return useQuery({
		queryKey: ["client-link", url],
		queryFn: () => AppRuntime.runPromise(client.getClientLink(url)),
		enabled: enabled && !!url,
	});
}
