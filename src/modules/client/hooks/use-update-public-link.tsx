import { useMutation } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { UpdatePublicClientLink } from "../service/model/publicClientLink";

export default function useUpdatePublicLink() {
	const service = useService();
	const { client } = service;

	return useMutation({
		mutationFn: (updatePublicLink: UpdatePublicClientLink) =>
			AppRuntime.runPromise(client.updatePublicLink(updatePublicLink)),
	});
}
