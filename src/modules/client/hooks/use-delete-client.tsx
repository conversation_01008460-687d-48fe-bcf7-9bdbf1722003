import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { clientOptions } from "./client-options";

export default function useDeleteClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(client.delete(id)),
		onSuccess: (_, id) => {
			queryClient.setQueryData(queryKey, (old: any[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((c) => c.id === id);
					if (index !== -1) {
						draft.splice(index, 1);
					}
				}),
			);
		},
	});
}
