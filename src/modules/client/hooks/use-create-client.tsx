import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Client, CreateClient } from "../service/model/client";
import { clientOptions } from "./client-options";

export default function useCreateClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationFn: (newClient: CreateClient) =>
			AppRuntime.runPromise(client.create(newClient)),
		onSuccess: (id, newClient) => {
			queryClient.setQueryData(queryKey, (old: Client[] | undefined) =>
				create(old ?? [], (draft) => {
					draft.push({
						id,
						person: newClient.person,
						createdAt: new Date().toISOString(),
						updatedAt: null,
						deletedAt: null,
					} as Client);
				}),
			);
		},
	});
}
