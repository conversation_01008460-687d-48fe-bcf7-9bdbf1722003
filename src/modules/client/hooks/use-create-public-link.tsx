import { useMutation } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CreatePublicClientLink } from "../service/model/publicClientLink";

export default function useCreatePublicLink() {
	const service = useService();
	const { client } = service;

	return useMutation({
		mutationFn: (newPublicLink: CreatePublicClientLink) =>
			AppRuntime.runPromise(client.createPublicLink(newPublicLink)),
	});
}
