import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type UpdateClient, Client } from "../service/model/client";
import { clientOptions } from "./client-options";

export default function useEditeClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedClient: UpdateClient) =>
			AppRuntime.runPromise(client.update(updatedClient)),
		onSuccess: (_, updatedClient) => {
			queryClient.setQueryData(queryKey, (old: Client[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((c) => c.id === updatedClient.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							person: updatedClient.person,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);
		},
	});
}
