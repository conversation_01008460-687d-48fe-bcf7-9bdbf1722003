import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useDeleteClient from "../hooks/use-delete-client";
import type { Client } from "../service/model/client";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	client: Client;
}
export default function DeleteClientModal({
	isOpen,
	setIsOpen,
	client,
}: Props) {
	const { mutate } = useDeleteClient();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar cliente</h3>
				<p>¿Estás seguro de que quieres eliminar este cliente?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Cliente: {client.person.name} {client.person.fatherLastName}{" "}
					{client.person.motherLastName}
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(client.id, {
								onSuccess: () => {
									toast.success("Cliente eliminado");
									setIsOpen(false);
								},
								onError: (error) => {
									console.log(error);
									toast.error("Error al eliminar cliente");
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
