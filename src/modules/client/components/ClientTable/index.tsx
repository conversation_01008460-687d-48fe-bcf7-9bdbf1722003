import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "../../hooks/client-options";
import Table from "./table";

export default function ClientTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(clientOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table clients={data} />;
}
