import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Client } from "../../service/model/client";
import { columns } from "./columns";

interface Props {
	clients: Client[];
}

export default function Table({ clients }: Props) {
	const table = useReactTable({
		data: clients,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
