import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useEditeClient from "../../hooks/use-edite-client";
import type { Client } from "../../service/model/client";
import { CreateClientSchema } from "../CreateClientModal/schema";

export interface EditeClientModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	client: Client;
}

export default function useEditeClientModal({
	setIsOpen,
	client,
}: EditeClientModalProps) {
	const { mutate } = useEditeClient();

	const {
		id,
		person: { id: personId, ...personRest },
		...rest
	} = client;

	const form = useAppForm({
		defaultValues: {
			...personRest,
		} as CreateClientSchema,
		validators: {
			onChange: CreateClientSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id,
					person: {
						...value,
						id: personId,
					},
				},
				{
					onSuccess: () => {
						toast.success("Cliente actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
