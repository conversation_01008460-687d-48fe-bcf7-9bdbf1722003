import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import TextModal from "~/core/components/TextModal";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptionsById } from "../../hooks/client-options";
import EditeClientForm from "./EditeClientForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}
export default function EditeClientModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();
	const { data, isError, error, isPending } = useQuery({
		...clientOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(error);
		}
	}, [error]);

	if (isPending) return <TextModal text="Cargando..." />;

	if (isError)
		return (
			<TextModal
				text="No se pudo cargar el cliente"
				title={getErrorResult(error).error.code.toString()}
			/>
		);

	return (
		<EditeClientForm isOpen={isOpen} setIsOpen={setIsOpen} client={data} />
	);
}
