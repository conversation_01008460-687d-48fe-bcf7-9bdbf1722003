import * as v from "valibot";

export const CreateClientSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	fatherLastName: v.pipe(
		v.string("Debe ingresar un apellido"),
		v.min<PERSON>ength(1, "Debe tener al menos un caracter"),
	),
	motherLastName: v.pipe(
		v.string("Debe ingresar un apellido"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	email: v.union([
		v.pipe(
			v.string("Debe ingresar un email"),
			v.email("Debe ingresar un email valido"),
		),
		v.literal(""),
	]),
	address: v.optional(v.string()),
	phone: v.optional(v.string()),
	birthDate: v.nullable(v.string()),
	gender: v.boolean(),
	document: v.pipe(
		v.string("Debe ingresar un documento"),
		v.min<PERSON>ength(8, "Debe tener al menos 8 caracteres"),
	),
	documentType: v.number(),
});
export type CreateClientSchema = v.InferOutput<typeof CreateClientSchema>;
