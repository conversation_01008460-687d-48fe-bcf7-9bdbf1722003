import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateClient from "../../hooks/use-create-client";
import { CreateClientSchema } from "./schema";

export interface CreateClientModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	fatherLastName: "",
	motherLastName: "",
	email: "",
	address: "",
	phone: "",
	birthDate: "",
	gender: false,
	document: "",
	documentType: 0,
} as CreateClientSchema;

export default function useCreateClientModal({
	setIsOpen,
}: CreateClientModalProps) {
	const { mutate } = useCreateClient();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateClientSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					person: value,
				},
				{
					onSuccess: () => {
						toast.success("Cliente creado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
