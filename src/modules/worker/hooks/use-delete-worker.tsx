import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { workerOptions } from "./worker-options";

export default function useDeleteWorker() {
	const service = useService();
	const { worker } = service;
	const queryClient = useQueryClient();
	const queryKey = workerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-worker"],
		mutationFn: (workerId: string) =>
			AppRuntime.runPromise(worker.delete(workerId)),
		onMutate: async (workerId) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkers = queryClient.getQueryData(queryKey);

			if (previousWorkers) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkers, (draft) => {
						const index = draft.findIndex((w) => w.id === workerId);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousWorkers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousWorkers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
