import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const workerOptions = ({ worker }: serviceRegistry) =>
	queryOptions({
		queryKey: ["workers"],
		queryFn: () => AppRuntime.runPromise(worker.getAll()),
	});

export const workerOptionsById = ({ worker }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["workers", id],
		queryFn: () => AppRuntime.runPromise(worker.getById(id)),
	});
