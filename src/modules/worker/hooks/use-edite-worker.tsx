import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type UpdateWorker, Worker } from "../service/model/worker";
import { workerOptions } from "./worker-options";

export default function useEditeWorker() {
	const service = useService();
	const { worker } = service;
	const queryClient = useQueryClient();
	const queryKey = workerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-worker"],
		mutationFn: (updatedWorker: UpdateWorker) =>
			AppRuntime.runPromise(worker.update(updatedWorker)),
		onMutate: async (updatedWorker) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkers = queryClient.getQueryData(queryKey);

			if (previousWorkers) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkers, (draft) => {
						const index = draft.findIndex((w) => w.id === updatedWorker.id);
						if (index !== -1) {
							draft[index] = Worker.make({
								...updatedWorker,
								person: {
									...updatedWorker.person,
									createdAt: null,
									updatedAt: null,
									deletedAt: null,
								},
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							});
						}
					}),
				);
			}

			return { previousWorkers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousWorkers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
