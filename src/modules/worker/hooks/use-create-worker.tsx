import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type Create<PERSON>orker, Worker } from "../service/model/worker";
import { workerOptions } from "./worker-options";

export default function useCreateWorker() {
	const service = useService();
	const { worker } = service;
	const queryClient = useQueryClient();
	const queryKey = workerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-worker"],
		mutationFn: (newWorker: CreateWorker) =>
			AppRuntime.runPromise(worker.create(newWorker)),
		onMutate: async (newWorker) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkers = queryClient.getQueryData(queryKey);

			if (previousWorkers) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkers, (draft) => {
						draft.push(
							Worker.make({
								id: "new",
								...newWorker,
								person: {
									id: "new",
									...newWorker.person,
									createdAt: null,
									updatedAt: null,
									deletedAt: null,
								},
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							}),
						);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					Worker.make({
						id: "new",
						...newWorker,
						person: {
							id: "new",
							...newWorker.person,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						},
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
				]);
			}

			return { previousWorkers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousWorkers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
