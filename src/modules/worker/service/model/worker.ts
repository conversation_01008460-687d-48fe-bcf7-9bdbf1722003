import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	Person,
	UpdatePerson,
} from "~/person/service/model/person";

export enum WorkerPosition {
	MANAGER = 1,
	ADMINISTRATOR = 2,
	INTERN = 3,
	PSYCHOLOGIST = 4,
	THERAPIST = 5,
}

export const Worker = Schema.Struct({
	id: Schema.String,
	person: Person,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Worker = typeof Worker.Type;

export const CreateWorker = Schema.Struct({
	person: Create<PERSON><PERSON>,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
});
export type CreateWorker = typeof CreateWorker.Type;

export const UpdateWorker = Schema.Struct({
	id: Schema.String,
	person: Update<PERSON><PERSON>,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
});
export type UpdateWorker = typeof UpdateWorker.Type;
