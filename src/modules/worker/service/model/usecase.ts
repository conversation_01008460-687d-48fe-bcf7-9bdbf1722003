import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Update<PERSON><PERSON><PERSON>, Worker } from "./worker";

export class WorkerUsecase extends Effect.Tag("WorkerUsecase")<
	WorkerUsecase,
	{
		readonly getAll: () => Effect.Effect<Worker[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Worker, AppError>;
		readonly create: (worker: CreateWorker) => Effect.Effect<string, AppError>;
		readonly update: (worker: UpdateWorker) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
