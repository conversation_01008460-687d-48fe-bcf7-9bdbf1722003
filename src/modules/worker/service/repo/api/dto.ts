import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Create<PERSON>ersonApiFrom<PERSON><PERSON><PERSON>erson,
	<PERSON><PERSON><PERSON>,
	Person<PERSON>romApi,
	UpdatePersonApi,
	UpdatePersonApiFromUpdatePerson,
} from "~/person/service/repo/api/dto";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UpdateWorker, Worker } from "../../model/worker";

export const WorkerApi = Schema.Struct({
	id: Schema.String,
	person: Person<PERSON><PERSON>,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const WorkerFromApi = Schema.transform(WorkerApi, Worker, {
	strict: true,
	decode: (workerApi) => ({
		...workerApi,
		person: Schema.decodeUnknownSync(PersonFromApi)(workerApi.person),
		createdAt: workerApi.created_at,
		updatedAt: workerApi.updated_at,
		deletedAt: workerApi.deleted_at,
	}),
	encode: (worker) => ({
		...worker,
		person: Schema.encodeUnknownSync(PersonFromApi)(worker.person),
		created_at: worker.createdAt,
		updated_at: worker.updatedAt,
		deleted_at: worker.deletedAt,
	}),
});

export const WorkerListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(WorkerFromApi))),
	Schema.mutable(Schema.Array(Worker)),
	{
		strict: true,
		decode: (workerApiList) => (workerApiList ? workerApiList : []),
		encode: (workerList) => workerList,
	},
);

export const CreateWorkerApi = Schema.Struct({
	person: CreatePersonApi,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
});

export const CreateWorkerApiFromCreateWorker = Schema.transform(
	CreateWorker,
	CreateWorkerApi,
	{
		strict: true,
		encode: (workerApi) => ({
			...workerApi,
			person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
				workerApi.person,
			),
		}),
		decode: (worker) => ({
			...worker,
			person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
				worker.person,
			),
		}),
	},
);

export const CreateWorkerApiResponse = Schema.String;

export const UpdateWorkerApi = Schema.Struct({
	id: Schema.String,
	person: UpdatePersonApi,
	positions: Schema.mutable(Schema.Array(Schema.Number)),
});

export const UpdateWorkerApiFromUpdateWorker = Schema.transform(
	UpdateWorker,
	UpdateWorkerApi,
	{
		strict: true,
		encode: (workerApi) => ({
			...workerApi,
			person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				workerApi.person,
			),
		}),
		decode: (worker) => ({
			...worker,
			person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				worker.person,
			),
		}),
	},
);
