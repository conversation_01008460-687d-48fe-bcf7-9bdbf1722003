import { <PERSON>ttp<PERSON><PERSON> } from "@effect/platform";
import { <PERSON>, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import { WorkerRepository } from "../../model/repository";
import type { <PERSON><PERSON><PERSON>or<PERSON>, UpdateWorker, Worker } from "../../model/worker";
import {
	CreateWorkerApiFromCreateWorker,
	CreateWorkerApiResponse,
	UpdateWorkerApiFromUpdateWorker,
	WorkerFromApi,
	WorkerListFromApi,
} from "./dto";

const baseUrl = "/v1/workers";

const makeWorkerApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(WorkerListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(WorkerFromApi))),
		create: (worker: CreateWorker) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateWorkerApiFromCreateWorker)(worker),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateWorkerApiResponse))),
		update: (worker: UpdateWorker) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateWorkerApiFromUpdateWorker)(worker),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const workerApiRepoLive = Layer.effect(
	WorkerRepository,
	makeWorkerApiRepo,
);
