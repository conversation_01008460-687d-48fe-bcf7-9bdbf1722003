import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateWorker from "../../hooks/use-create-worker";
import { CreateWorkerSchema } from "./schema";

export interface CreateWorkerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	fatherLastName: "",
	motherLastName: "",
	email: "",
	address: "",
	phone: "",
	birthDate: "",
	gender: false,
	document: "",
	documentType: 0,
	positions: [],
} as CreateWorkerSchema;

export default function useCreateWorkerModal({
	setIsOpen,
}: CreateWorkerModalProps) {
	const { mutate } = useCreateWorker();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateWorkerSchema,
		},
		onSubmit: ({ value }) => {
			const { positions, ...person } = value;
			mutate(
				{
					person,
					positions: value.positions ?? [],
				},
				{
					onSuccess: () => {
						toast.success("Trabajador creado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
