import * as v from "valibot";

export const CreateWorkerSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar una cuenta"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	fatherLastName: v.pipe(
		v.string("Debe ingresar un apellido"),
		v.min<PERSON>ength(1, "Debe tener al menos un caracter"),
	),
	motherLastName: v.pipe(
		v.string("Debe ingresar un apellido"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	email: v.union([
		v.pipe(
			v.string("Debe ingresar un email"),
			v.email("Debe ingresar un email valido"),
		),
		v.literal(""),
	]),
	address: v.optional(v.string()),
	phone: v.optional(v.string()),
	birthDate: v.nullable(v.string()),
	gender: v.boolean(),
	document: v.pipe(
		v.string("Debe ingresar un documento"),
		v.min<PERSON>eng<PERSON>(8, "Debe tener al menos 8 caracteres"),
	),
	documentType: v.number(),
	positions: v.array(v.number()),
});
export type CreateWorkerSchema = v.InferOutput<typeof CreateWorkerSchema>;
