import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { workerOptions } from "../../hooks/worker-options";
import Table from "./table";

export default function WorkerTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(workerOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table workers={data} />;
}
