import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Edit2, Trash } from "lucide-react";
import { useState } from "react";
import { DocumentType } from "~/person/service/model/person";
import { type Worker, WorkerPosition } from "../../service/model/worker";
import DeleteWorkerModal from "../DeleteWorkerModal";
import EditeWorkerModal from "../EditeWorkerModal";

const columnHelper = createColumnHelper<Worker>();

export const columns = [
	columnHelper.accessor("person.name", {
		header: "Name",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.display({
		header: "Apellidos",
		cell: ({ row }) =>
			`${row.original.person.fatherLastName} ${row.original.person.motherLastName}`,
	}),
	columnHelper.accessor("person.email", {
		header: "Email",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.accessor("person.address", {
		header: "Dirección",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.accessor("person.phone", {
		header: "Teléfono",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.accessor("person.birthDate", {
		header: "Fecha de Nacimiento",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.accessor("person.document", {
		header: "Documento",
		cell: (info) => info.getValue() ?? "-",
	}),
	columnHelper.accessor("person.documentType", {
		header: "Tipo de Documento",
		cell: (info) => {
			const documentTypeText: Record<number, string> = {
				[DocumentType.DNI]: "DNI",
				[DocumentType.PASAPORTE]: "Pasaporte",
				[DocumentType.RUC]: "RUC",
			};

			return documentTypeText[info.getValue()] ?? "-";
		},
	}),
	columnHelper.accessor("positions", {
		header: "Positions",
		cell: (info) => {
			const positionsText: Record<number, string> = {
				[WorkerPosition.MANAGER]: "Gerente",
				[WorkerPosition.ADMINISTRATOR]: "Administrador",
				[WorkerPosition.INTERN]: "Interno",
				[WorkerPosition.PSYCHOLOGIST]: "Psicólogo",
				[WorkerPosition.THERAPIST]: "Terapeuta",
			};

			return info
				.getValue()
				.map((position) => positionsText[position])
				.join(", ");
		},
	}),
	columnHelper.display({
		header: "Acciones",
		cell: ({ row }) => {
			const [isOpen, setIsOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			return (
				<>
					<div className="flex gap-2">
						<button
							type="button"
							className="btn btn-circle btn-primary"
							onClick={() => setIsOpen(true)}
						>
							<Edit2 size={16} />
						</button>
						<button
							type="button"
							className="btn btn-circle btn-error"
							onClick={() => setIsDeleteOpen(true)}
						>
							<Trash size={16} />
						</button>
					</div>
					<EditeWorkerModal
						isOpen={isOpen}
						setIsOpen={setIsOpen}
						id={row.original.id}
					/>
					<DeleteWorkerModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						id={row.original.id}
					/>
				</>
			);
		},
	}),
];
