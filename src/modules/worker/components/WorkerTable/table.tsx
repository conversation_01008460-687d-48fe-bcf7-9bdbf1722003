import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Worker } from "../../service/model/worker";
import { columns } from "./columns";
interface Props {
	workers: Worker[];
}
export default function Table({ workers }: Props) {
	const table = useReactTable({
		data: workers,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
