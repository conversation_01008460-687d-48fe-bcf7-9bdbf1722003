import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useDeleteWorker from "../hooks/use-delete-worker";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}
export default function DeleteWorkerModal({ isOpen, setIsOpen, id }: Props) {
	const { mutate } = useDeleteWorker();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar trabajador</h3>
				<p>¿Estás seguro de que quieres eliminar este trabajador?</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(id, {
								onSuccess: () => {
									toast.success("Trabajador eliminado");
									setIsOpen(false);
								},
								onError: (error) => {
									console.log(error);
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
