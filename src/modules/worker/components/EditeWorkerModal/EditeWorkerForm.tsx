import { Calendar, FileText, Mail, MapPin, Phone, User } from "lucide-react";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { WorkerPosition } from "../../service/model/worker";
import useEditeWorkerModal, {
	type EditeWorkerModalProps,
} from "./use-edite-worker-modal";

export default function EditeWorkerForm({
	isOpen,
	setIsOpen,
	worker,
}: EditeWorkerModalProps) {
	const { form, handleClose } = useEditeWorkerModal({
		isOpen,
		setIsOpen,
		worker,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Trabajador</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="positions"
								children={({ FSComboBoxField }) => (
									<FSComboBoxField
										label="Posición"
										placeholder="Posición"
										isMultiple
										options={[
											{ value: WorkerPosition.MANAGER, label: "Gerente" },
											{
												value: WorkerPosition.ADMINISTRATOR,
												label: "Administrador",
											},
											{ value: WorkerPosition.INTERN, label: "Interno" },
											{
												value: WorkerPosition.PSYCHOLOGIST,
												label: "Psicólogo",
											},
											{
												value: WorkerPosition.THERAPIST,
												label: "Terapeuta",
											},
										]}
									/>
								)}
							/>
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="fatherLastName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Paterno"
										placeholder="Apellido Paterno"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="motherLastName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Materno"
										placeholder="Apellido Materno"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="email"
								children={({ FSTextField }) => (
									<FSTextField
										label="Correo Electrónico"
										placeholder="<EMAIL>"
										prefixComponent={<Mail size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="address"
								children={({ FSTextField }) => (
									<FSTextField
										label="Dirección"
										placeholder="Dirección"
										prefixComponent={<MapPin size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="phone"
								children={({ FSTextField }) => (
									<FSTextField
										label="Teléfono"
										placeholder="Teléfono"
										prefixComponent={<Phone size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="birthDate"
								children={({ FSTextField }) => (
									<FSTextField
										label="Fecha de Nacimiento"
										placeholder="YYYY-MM-DD"
										type="date"
										prefixComponent={<Calendar size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="document"
								children={({ FSTextField }) => (
									<FSTextField
										label="Documento"
										placeholder="Número de Documento"
										prefixComponent={<FileText size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="documentType"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Tipo de Documento"
										placeholder="Tipo de Documento"
										isNumber
										options={[
											{
												value: 0,
												label: "DNI",
											},
											{
												value: 1,
												label: "Pasaporte",
											},
											{
												value: 2,
												label: "RUC",
											},
										]}
									/>
								)}
							/>
							<form.AppField
								name="gender"
								children={({ FSToggleField }) => (
									<FSToggleField
										label="Género"
										trueLabel="Masculino"
										falseLabel="Femenino"
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<button type="submit" className="btn btn-primary">
								Crear
							</button>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
