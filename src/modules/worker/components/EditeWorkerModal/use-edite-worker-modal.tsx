import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useEditeWorker from "../../hooks/use-edite-worker";
import type { Worker } from "../../service/model/worker";
import { CreateWorkerSchema } from "../CreateWorkerModal/schema";

export interface EditeWorkerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	worker: Worker;
}
export default function useEditeWorkerModal({
	setIsOpen,
	worker,
}: EditeWorkerModalProps) {
	const { mutate } = useEditeWorker();
	const { id, createdAt, updatedAt, deletedAt, ...rest } = worker;
	const {
		id: personId,
		createdAt: personCreatedAt,
		updatedAt: personUpdatedAt,
		deletedAt: personDeletedAt,
		...personRest
	} = rest.person;

	const form = useAppForm({
		defaultValues: {
			...personRest,
			positions: rest.positions,
		} as CreateWorkerSchema,
		validators: {
			onChange: CreateWorkerSchema,
		},
		onSubmit: ({ value }) => {
			const { positions, ...person } = value;
			mutate(
				{
					id,
					person: {
						...person,
						id: personId,
					},
					positions: value.positions ?? [],
				},
				{
					onSuccess: () => {
						toast.success("Trabajador actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
