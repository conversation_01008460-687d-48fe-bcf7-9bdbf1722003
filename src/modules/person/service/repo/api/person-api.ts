import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { <PERSON><PERSON><PERSON><PERSON>, Person } from "../../model/person";
import { PersonRepository } from "../../model/repository";
import {
	CreatePersonApiFromCreatePerson,
	CreatePersonApiResponse,
	PersonFromApi,
	PersonListFromApi,
} from "./dto";

const baseUrl = "/v1/person";

const makePersonApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(PersonListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(PersonFromApi))),
		create: (person: CreatePerson) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(person),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreatePersonApiResponse))),
		update: (person: Person) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.encodeUnknownSync(PersonFromApi)(person),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson({ id }),
				})
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const personApiRepoLive = Layer.effect(
	PersonRepository,
	makePersonApiRepo,
);
