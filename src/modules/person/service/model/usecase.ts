import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { Person } from "./person";

export class PersonUsecase extends Effect.Tag("PersonUsecase")<
	PersonUsecase,
	{
		readonly getAll: () => Effect.Effect<Person[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Person, AppError>;
		readonly create: (person: Person) => Effect.Effect<string, AppError>;
		readonly update: (person: Person) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
