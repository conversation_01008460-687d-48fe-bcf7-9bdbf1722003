import { useStore } from "@tanstack/react-store";
import type { Schedule } from "~/modules/schedule/service/model/schedule";
import { sessionActions, sessionStore } from "../store/session";

interface TurnSelectProps {
	schedule: Schedule;
}

export default function TurnSelect({ schedule }: TurnSelectProps) {
	const { selectedTurn } = useStore(sessionStore);
	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
	};

	const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
		const turnId = event.target.value;
		if (turnId === "") {
			sessionActions.setTurn(null);
		} else {
			const turn = schedule.turns.find((t) => t.id === turnId);
			sessionActions.setTurn(turn || null);
		}
	};

	return (
		<div className="form-control w-full">
			<label htmlFor="turn-select" className="label">
				<span className="label-text">Turno</span>
			</label>
			<select
				id="turn-select"
				className="select select-bordered w-full"
				value={selectedTurn?.id || ""}
				onChange={handleSelectChange}
			>
				<option value="">Seleccionar turno...</option>
				{schedule.turns.map((turn) => (
					<option key={turn.id} value={turn.id}>
						{turn.name} ({formatTime(turn.startTime)} -{" "}
						{formatTime(turn.endTime)})
					</option>
				))}
			</select>
		</div>
	);
}
