import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import ComboBox from "~/core/components/ComboBox";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import type { Client } from "~/modules/client/service/model/client";
import { sessionOptions } from "~/modules/session/hooks/session-options";
import useCreateSession from "~/modules/session/hooks/use-create-session";
import useDeleteSession from "~/modules/session/hooks/use-delete-session";
import type { Session } from "~/modules/session/service/model/session";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import type { Worker } from "~/modules/worker/service/model/worker";
import { sessionStore } from "../store/session";

interface SessionModalProps {
	isOpen: boolean;
	onClose: () => void;
	dayIndex: number;
	timeIndex: number;
	existingSession: Session | undefined;
}

export default function SessionModal({
	isOpen,
	onClose,
	dayIndex,
	timeIndex,
	existingSession,
}: SessionModalProps) {
	const svc = useService();
	const { mutate: createSession } = useCreateSession();
	const { mutate: deleteSession } = useDeleteSession();
	const {
		selectedClient: preSelectedClient,
		selectedWorker: preSelectedWorker,
		selectedTurn,
	} = useStore(sessionStore);

	const turnId = selectedTurn?.id || "";

	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);

	const {
		data: clients,
		isError: clientsError,
		error: clientError,
		isPending: clientsPending,
	} = useQuery({
		...clientOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		isError: workersError,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	// Get ALL sessions to filter availability properly
	const { data: allSessionsData } = useQuery({
		...sessionOptions(svc),
		enabled: isOpen,
	});

	// Filter sessions for the current turn only
	const allSessions = (allSessionsData || []).filter(
		(session) => session.turnId === turnId,
	);

	// Set initial values when modal opens
	useEffect(() => {
		if (isOpen) {
			if (existingSession) {
				setSelectedClient(existingSession.client);
				setSelectedWorker(existingSession.worker);
			} else {
				setSelectedClient(preSelectedClient);
				setSelectedWorker(preSelectedWorker);
			}
		}
	}, [isOpen, existingSession, preSelectedClient, preSelectedWorker]);

	// Reset when modal closes
	useEffect(() => {
		if (!isOpen) {
			setSelectedClient(null);
			setSelectedWorker(null);
		}
	}, [isOpen]);

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	// Helper function to check if a client/worker is busy at this time slot
	const isClientBusyAtTimeSlot = (clientId: string) => {
		return allSessions.some(
			(session) =>
				session.client.id === clientId &&
				session.day === dayIndex &&
				session.time === timeIndex &&
				session.id !== existingSession?.id, // Don't exclude current session
		);
	};

	const isWorkerBusyAtTimeSlot = (workerId: string) => {
		return allSessions.some(
			(session) =>
				session.worker.id === workerId &&
				session.day === dayIndex &&
				session.time === timeIndex &&
				session.id !== existingSession?.id, // Don't exclude current session
		);
	};

	// Filter out busy clients and workers
	const availableClients =
		clients?.filter((client) => {
			// Don't filter if this is the current session's client
			if (existingSession && client.id === existingSession.client.id) {
				return true;
			}
			// Filter out busy clients and clients with ID "0" (busy placeholder)
			return client.id !== "0" && !isClientBusyAtTimeSlot(client.id);
		}) || [];

	const availableWorkers =
		workers?.filter((worker) => {
			// Don't filter if this is the current session's worker
			if (existingSession && worker.id === existingSession.worker.id) {
				return true;
			}
			// Filter out busy workers
			return !isWorkerBusyAtTimeSlot(worker.id);
		}) || [];

	const clientOptions_data = availableClients.map((client) => ({
		value: client.id,
		label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
	}));

	const workerOptions_data = availableWorkers.map((worker) => ({
		value: worker.id,
		label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
	}));

	const handleClientChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const client = availableClients.find((c) => c.id === option.value);
			setSelectedClient(client || null);

			// Auto-save if we have both client and worker
			if (client && selectedWorker) {
				autoSaveSession(client, selectedWorker);
			}
		} else {
			setSelectedClient(null);
		}
	};

	const handleWorkerChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const worker = availableWorkers.find((w) => w.id === option.value);
			setSelectedWorker(worker || null);

			// Auto-save if we have both client and worker
			if (worker && selectedClient) {
				autoSaveSession(selectedClient, worker);
			}
		} else {
			setSelectedWorker(null);
		}
	};

	// Helper function to auto-save session
	const autoSaveSession = (client: Client, worker: Worker) => {
		let finalClientId: string;
		let finalWorkerId: string;

		if (existingSession) {
			// For existing sessions, use the selected values from the modal
			finalClientId = client.id;
			finalWorkerId = worker.id;
		} else if (preSelectedClient) {
			// Client was pre-selected from store, use worker from modal
			finalClientId = preSelectedClient.id;
			finalWorkerId = worker.id;
		} else if (preSelectedWorker) {
			// Worker was pre-selected from store, use client from modal
			finalWorkerId = preSelectedWorker.id;
			finalClientId = client.id;
		} else {
			// This shouldn't happen, but handle it just in case
			finalClientId = client.id;
			finalWorkerId = worker.id;
		}

		// Check if we're replacing an existing session
		const isReplacing =
			existingSession &&
			(existingSession.client.id !== finalClientId ||
				existingSession.worker.id !== finalWorkerId);

		if (isReplacing) {
			// Delete existing session first, then create new one
			deleteSession(existingSession.id, {
				onSuccess: () => {
					// Create new session after successful deletion
					createSession(
						{
							clientId: finalClientId,
							workerId: finalWorkerId,
							turnId,
							day: dayIndex,
							time: timeIndex,
						},
						{
							onSuccess: () => {
								toast.success("Sesión reemplazada exitosamente");
								onClose();
							},
							onError: (error) => {
								console.log(error);
								const { error: errorResult } = getErrorResult(error);
								toast.error(errorResult.message);
							},
						},
					);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			});
		} else if (!existingSession) {
			// Create new session only if there's no existing session
			createSession(
				{
					clientId: finalClientId,
					workerId: finalWorkerId,
					turnId,
					day: dayIndex,
					time: timeIndex,
				},
				{
					onSuccess: () => {
						toast.success("Sesión creada exitosamente");
						onClose();
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				},
			);
		}
	};

	const handleDelete = () => {
		if (!existingSession) return;

		deleteSession(existingSession.id, {
			onSuccess: () => {
				toast.success("Sesión eliminada exitosamente");
				onClose();
			},
			onError: (error) => {
				console.log(error);
				const { error: errorResult } = getErrorResult(error);
				toast.error(errorResult.message);
			},
		});
	};

	const handleMakeBusy = () => {
		if (!preSelectedWorker) {
			toast.error(
				"Solo se puede marcar como ocupado cuando hay un trabajador seleccionado",
			);
			return;
		}

		// Create a busy session with clientId = "0"
		createSession(
			{
				clientId: "0",
				workerId: preSelectedWorker.id,
				turnId,
				day: dayIndex,
				time: timeIndex,
			},
			{
				onSuccess: () => {
					toast.success("Horario marcado como ocupado");
					onClose();
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const selectedClientOption = selectedClient
		? {
				value: selectedClient.id,
				label: `${selectedClient.person.name} ${selectedClient.person.fatherLastName} ${selectedClient.person.motherLastName}`,
				data: selectedClient,
			}
		: null;

	const selectedWorkerOption = selectedWorker
		? {
				value: selectedWorker.id,
				label: `${selectedWorker.person.name} ${selectedWorker.person.fatherLastName} ${selectedWorker.person.motherLastName}`,
				data: selectedWorker,
			}
		: null;

	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box w-3/12 max-w-5xl">
				<CloseModal onClose={onClose} />
				<h3 className="font-bold text-lg">
					{existingSession ? "Editar Sesión" : "Crear Sesión"}
				</h3>

				<div className="py-4">
					<p className="mb-4 text-base-content/70 text-sm">
						{days[dayIndex]} - Horario {timeIndex + 1}
					</p>

					<div className="space-y-4">
						{preSelectedWorker ? (
							<div>
								{clientsError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(clientError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={clientOptions_data}
										value={selectedClientOption}
										onChange={handleClientChange}
										placeholder="Buscar cliente..."
										isLoading={clientsPending}
										className="w-full"
									/>
								)}
							</div>
						) : null}

						{/* Show Worker Selection if client was pre-selected, or if editing existing session */}
						{preSelectedClient ? (
							<div>
								{workersError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(workerError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={workerOptions_data}
										value={selectedWorkerOption}
										onChange={handleWorkerChange}
										placeholder="Buscar trabajador..."
										isLoading={workersPending}
										className="w-full"
									/>
								)}
							</div>
						) : null}

						{/* Busy/Free Controls - alongside comboboxes */}
						{!existingSession && preSelectedWorker && (
							<div>
								<button
									type="button"
									className="btn btn-warning w-full"
									onClick={handleMakeBusy}
								>
									Marcar como Ocupado
								</button>
							</div>
						)}

						{/* Delete/Free existing session controls */}
						{existingSession && (
							<div>
								<button
									type="button"
									className={`btn w-full ${existingSession.client.id === "0" ? "btn-success" : "btn-error"}`}
									onClick={handleDelete}
								>
									{existingSession.client.id === "0"
										? "Liberar Horario"
										: "Eliminar Sesión"}
								</button>
							</div>
						)}
					</div>
				</div>
				<div className="modal-action">
					<button type="button" className="btn btn-ghost" onClick={onClose}>
						Cancelar
					</button>
				</div>
			</div>
		</div>
	);
}
