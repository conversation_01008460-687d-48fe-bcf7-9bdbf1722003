import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { scheduleOptions } from "~/modules/schedule/hooks/schedule-options";
import { sessionActions, sessionStore } from "../store/session";

export default function ScheduleSelect() {
	const { selectedSchedule } = useStore(sessionStore);
	const svc = useService();
	const { data, isError, error, isPending } = useQuery(scheduleOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
		const scheduleId = event.target.value;
		if (scheduleId === "") {
			sessionActions.setSchedule(null);
		} else {
			const schedule = data?.find((s) => s.id === scheduleId);
			sessionActions.setSchedule(schedule || null);
		}
	};

	if (isPending) {
		return (
			<div className="flex items-center gap-2">
				<span className="loading loading-spinner loading-sm" />
				<span>Cargando horarios...</span>
			</div>
		);
	}

	if (isError) {
		return (
			<div className="alert alert-error">
				<span>Error: {getErrorResult(error).error.message}</span>
			</div>
		);
	}

	return (
		<div className="form-control w-full">
			<label htmlFor="schedule-select" className="label">
				<span className="label-text">Horario</span>
			</label>
			<select
				id="schedule-select"
				className="select select-bordered w-full"
				value={selectedSchedule?.id || ""}
				onChange={handleSelectChange}
			>
				<option value="">Seleccionar horario...</option>
				{data?.map((schedule) => (
					<option key={schedule.id} value={schedule.id}>
						{schedule.name}
					</option>
				))}
			</select>
		</div>
	);
}
