import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { DeleteManySession } from "../service/model/session";
import { sessionStore } from "../store/session";
import { sessionOptionsByWorkerAndTurn } from "./session-options";

export default function useBulkDeleteSessions() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const [selectedWorker, selectedTurn] = useStore(sessionStore, (state) => [
		state.selectedWorker,
		state.selectedTurn,
	]);

	return useMutation({
		mutationFn: (request: DeleteManySession) =>
			AppRuntime.runPromise(session.deleteMany(request)),
		onMutate: async (request) => {
			if (selectedWorker && selectedTurn) {
				const sessionsByWorkerQueryKey = sessionOptionsByWorkerAndTurn(
					service,
					selectedWorker.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByWorkerQueryKey,
				});

				const sessionsByWorker = queryClient.getQueryData(
					sessionsByWorkerQueryKey,
				);

				if (sessionsByWorker) {
					queryClient.setQueryData(
						sessionsByWorkerQueryKey,
						create(sessionsByWorker, (draft) => {
							const index = draft.findIndex((s) => s.id === request.ids[0]);
							if (index !== -1) {
								draft.splice(index, 1);
							}
						}),
					);
				}

				return {
					previousSessions: sessionsByWorker,
					queryKey: sessionsByWorkerQueryKey,
				};
			}

			return { previousSessions: [], queryKey: [] };
		},
		onError: (_, __, context) => {
			if (context) {
				queryClient.setQueryData(context.queryKey, context.previousSessions);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
