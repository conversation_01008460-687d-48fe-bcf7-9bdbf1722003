import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { sessionStore } from "../store/session";
import {
	sessionOptionsByClientAndTurn,
	sessionOptionsByWorkerAndTurn,
} from "./session-options";

export default function useDeleteSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const [selectedClient, selectedWorker, selectedTurn] = useStore(
		sessionStore,
		(state) => [state.selectedClient, state.selectedWorker, state.selectedTurn],
	);

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(session.delete(id)),
		onMutate: async (id: string) => {
			if (selectedClient && selectedTurn) {
				const sessionsByClientQueryKey = sessionOptionsByClientAndTurn(
					service,
					selectedClient.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByClientQueryKey,
				});

				const sessionsByClient = queryClient.getQueryData(
					sessionsByClientQueryKey,
				);

				if (sessionsByClient) {
					queryClient.setQueryData(
						sessionsByClientQueryKey,
						create(sessionsByClient, (draft) => {
							const index = draft.findIndex((s) => s.id === id);
							if (index !== -1) {
								draft.splice(index, 1);
							}
						}),
					);
				}

				return {
					previousSessions: sessionsByClient,
					queryKey: sessionsByClientQueryKey,
				};
			}

			if (selectedWorker && selectedTurn) {
				const sessionsByWorkerQueryKey = sessionOptionsByWorkerAndTurn(
					service,
					selectedWorker.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByWorkerQueryKey,
				});

				const sessionsByWorker = queryClient.getQueryData(
					sessionsByWorkerQueryKey,
				);

				if (sessionsByWorker) {
					queryClient.setQueryData(
						sessionsByWorkerQueryKey,
						create(sessionsByWorker, (draft) => {
							const index = draft.findIndex((s) => s.id === id);
							if (index !== -1) {
								draft.splice(index, 1);
							}
						}),
					);
				}

				return {
					previousSessions: sessionsByWorker,
					queryKey: sessionsByWorkerQueryKey,
				};
			}

			return { previousSessions: [], queryKey: [] };
		},
		onError: (_, __, context) => {
			if (context) {
				queryClient.setQueryData(context.queryKey, context.previousSessions);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
