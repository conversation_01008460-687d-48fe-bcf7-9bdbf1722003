import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { create } from "mutative";
import { Client } from "~/client/service/model/client";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { Worker } from "~/worker/service/model/worker";
import { type CreateSession, Session } from "../service/model/session";
import { sessionStore } from "../store/session";
import { newPerson } from "../utils/person";
import {
	sessionOptionsByClientAndTurn,
	sessionOptionsByWorkerAndTurn,
} from "./session-options";

export default function useCreateSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const [selectedClient, selectedWorker, selectedTurn] = useStore(
		sessionStore,
		(state) => [state.selectedClient, state.selectedWorker, state.selectedTurn],
	);

	return useMutation({
		mutationFn: (newSession: CreateSession) =>
			AppRuntime.runPromise(session.create(newSession)),
		onMutate: async (newSession) => {
			console.log(newSession);
			if (selectedClient && selectedTurn) {
				const sessionsByClientQueryKey = sessionOptionsByClientAndTurn(
					service,
					selectedClient.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByClientQueryKey,
				});

				const sessionsByClient = queryClient.getQueryData(
					sessionsByClientQueryKey,
				);

				const newSessionEl = Session.make({
					id: "new",
					...newSession,
					client: selectedClient,
					worker: Worker.make({
						id: "new",
						person: newPerson,
						positions: [],
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
					createdAt: null,
					updatedAt: null,
					deletedAt: null,
				});

				if (sessionsByClient) {
					queryClient.setQueryData(
						sessionsByClientQueryKey,
						create(sessionsByClient, (draft) => {
							draft.push(newSessionEl);
						}),
					);
				} else {
					queryClient.setQueryData(sessionsByClientQueryKey, [newSessionEl]);
				}

				return {
					previousSessions: sessionsByClient,
					queryKey: sessionsByClientQueryKey,
				};
			}

			if (selectedWorker && selectedTurn) {
				const sessionsByWorkerQueryKey = sessionOptionsByWorkerAndTurn(
					service,
					selectedWorker.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByWorkerQueryKey,
				});

				const sessionsByWorker = queryClient.getQueryData(
					sessionsByWorkerQueryKey,
				);

				const newSessionEl = Session.make({
					id: "new",
					...newSession,
					client: Client.make({
						id: "new",
						person: newPerson,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
					worker: selectedWorker,
					createdAt: null,
					updatedAt: null,
					deletedAt: null,
				});

				if (sessionsByWorker) {
					queryClient.setQueryData(
						sessionsByWorkerQueryKey,
						create(sessionsByWorker, (draft) => {
							draft.push(newSessionEl);
						}),
					);
				} else {
					queryClient.setQueryData(sessionsByWorkerQueryKey, [newSessionEl]);
				}

				return {
					previousSessions: sessionsByWorker,
					queryKey: sessionsByWorkerQueryKey,
				};
			}

			return { previousSessions: [], queryKey: [] };
		},
		onError: (_, __, context) => {
			if (context) {
				queryClient.setQueryData(context.queryKey, context.previousSessions);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
