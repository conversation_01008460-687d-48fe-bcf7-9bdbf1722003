import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type {
	CreateManySession,
	CreateSession,
	DeleteManySession,
	Session,
	UpdateSession,
} from "./session";

export class SessionUsecase extends Effect.Tag("SessionUsecase")<
	SessionUsecase,
	{
		readonly getAll: () => Effect.Effect<Session[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Session, AppError>;
		readonly getByClientAndTurn: (
			clientId: string,
			turnId: string,
		) => Effect.Effect<Session[], AppError>;
		readonly getByWorkerAndTurn: (
			workerId: string,
			turnId: string,
		) => Effect.Effect<Session[], AppError>;
		readonly create: (
			session: CreateSession,
		) => Effect.Effect<string, AppError>;
		readonly update: (session: UpdateSession) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly createMany: (
			sessions: CreateManySession,
		) => Effect.Effect<readonly string[], AppError>;
		readonly deleteMany: (
			request: DeleteManySession,
		) => Effect.Effect<void, AppError>;
	}
>() {}
