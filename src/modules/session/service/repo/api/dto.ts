import { Schema } from "effect";
import { <PERSON><PERSON><PERSON><PERSON>, Client<PERSON>rom<PERSON><PERSON> } from "~/client/service/repo/api/dto";
import { WorkerApi, WorkerFromApi } from "~/worker/service/repo/api/dto";
import {
	CreateManySession,
	CreateSession,
	DeleteManySession,
	Session,
	UpdateSession,
} from "../../model/session";

export const SessionApi = Schema.Struct({
	id: Schema.String,
	client: Client<PERSON><PERSON>,
	worker: Worker<PERSON><PERSON>,
	turn_id: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const SessionFromApi = Schema.transform(SessionApi, Session, {
	strict: true,
	decode: (sessionApi) => ({
		id: sessionApi.id,
		client: Schema.decodeUnknownSync(ClientFromApi)(sessionApi.client),
		worker: Schema.decodeUnknownSync(WorkerFromApi)(sessionApi.worker),
		turnId: sessionApi.turn_id,
		day: sessionApi.day,
		time: sessionApi.time,
		createdAt: sessionApi.created_at,
		updatedAt: sessionApi.updated_at,
		deletedAt: sessionApi.deleted_at,
	}),
	encode: (session) => ({
		id: session.id,
		client: Schema.encodeUnknownSync(ClientFromApi)(session.client),
		worker: Schema.encodeUnknownSync(WorkerFromApi)(session.worker),
		turn_id: session.turnId,
		day: session.day,
		time: session.time,
		created_at: session.createdAt,
		updated_at: session.updatedAt,
		deleted_at: session.deletedAt,
	}),
});

export const SessionListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(SessionFromApi))),
	Schema.mutable(Schema.Array(Session)),
	{
		strict: true,
		decode: (sessionApiList) => (sessionApiList ? sessionApiList : []),
		encode: (sessionList) => sessionList,
	},
);

export const CreateSessionApi = Schema.Struct({
	client_id: Schema.String,
	worker_id: Schema.String,
	turn_id: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
});

export const CreateSessionApiFromCreateSession = Schema.transform(
	CreateSession,
	CreateSessionApi,
	{
		strict: true,
		decode: (session) => ({
			client_id: session.clientId,
			worker_id: session.workerId,
			turn_id: session.turnId,
			day: session.day,
			time: session.time,
		}),
		encode: (sessionApi) => ({
			clientId: sessionApi.client_id,
			workerId: sessionApi.worker_id,
			turnId: sessionApi.turn_id,
			day: sessionApi.day,
			time: sessionApi.time,
		}),
	},
);

export const CreateSessionApiResponse = Schema.String;

export const UpdateSessionApi = Schema.Struct({
	id: Schema.String,
	client_id: Schema.String,
	worker_id: Schema.String,
	turn_id: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
});

export const UpdateSessionApiFromUpdateSession = Schema.transform(
	UpdateSession,
	UpdateSessionApi,
	{
		strict: true,
		decode: (session) => ({
			id: session.id,
			client_id: session.clientId,
			worker_id: session.workerId,
			turn_id: session.turnId,
			day: session.day,
			time: session.time,
		}),
		encode: (sessionApi) => ({
			id: sessionApi.id,
			clientId: sessionApi.client_id,
			workerId: sessionApi.worker_id,
			turnId: sessionApi.turn_id,
			day: sessionApi.day,
			time: sessionApi.time,
		}),
	},
);

// Bulk operations DTOs
export const CreateManySessionApi = Schema.Struct({
	sessions: Schema.Array(CreateSessionApi),
});

export const CreateManySessionApiFromCreateManySession = Schema.transform(
	CreateManySession,
	CreateManySessionApi,
	{
		strict: true,
		decode: (request) => ({
			sessions: request.sessions.map((session) =>
				Schema.decodeUnknownSync(CreateSessionApiFromCreateSession)(session),
			),
		}),
		encode: (requestApi) => ({
			sessions: requestApi.sessions.map((sessionApi) =>
				Schema.encodeUnknownSync(CreateSessionApiFromCreateSession)(sessionApi),
			),
		}),
	},
);

export const CreateManySessionApiResponse = Schema.Array(Schema.String);

export const DeleteManySessionApi = Schema.Struct({
	ids: Schema.Array(Schema.String),
});

export const DeleteManySessionApiFromDeleteManySession = Schema.transform(
	DeleteManySession,
	DeleteManySessionApi,
	{
		strict: true,
		decode: (request) => ({
			ids: request.ids,
		}),
		encode: (requestApi) => ({
			ids: requestApi.ids,
		}),
	},
);
