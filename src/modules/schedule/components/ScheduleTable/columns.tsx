import { Link } from "@tanstack/react-router";
import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import type { Schedule } from "../../service/model/schedule";
import DeleteScheduleModal from "../DeleteScheduleModal";

const columnHelper = createColumnHelper<Schedule>();

const formatTime = (time: number) => {
	const hours = Math.floor(time / 100);
	const minutes = time % 100;
	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

const formatDate = (dateString: string | null) => {
	if (!dateString) return "N/A";
	return new Date(dateString).toLocaleDateString();
};

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => <div className="font-bold">{info.getValue()}</div>,
	}),
	columnHelper.accessor("sessionDuration", {
		header: "Duración Sesión",
		cell: (info) => `${info.getValue()} min`,
	}),
	columnHelper.accessor("breakDuration", {
		header: "Duración Descanso",
		cell: (info) => `${info.getValue()} min`,
	}),
	columnHelper.accessor("turns", {
		header: "Turnos",
		cell: (info) => (
			<div className="flex flex-wrap gap-2">
				{info.getValue().map((turn) => (
					<div key={turn.id} className="badge badge-soft">
						{turn.name}: {formatTime(turn.startTime)} -{" "}
						{formatTime(turn.endTime)}
					</div>
				))}
			</div>
		),
	}),
	columnHelper.accessor("createdAt", {
		header: "Fecha Creación",
		cell: (info) => formatDate(info.getValue()),
	}),
	columnHelper.display({
		header: "Acciones",
		cell: ({ row }) => {
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			return (
				<>
					<div className="flex gap-2">
						<Link
							to="/admin/schedules/edit/$id"
							params={{ id: row.original.id }}
							className="btn btn-circle btn-primary"
						>
							<Edit size={16} />
						</Link>
						<button
							type="button"
							className="btn btn-circle btn-error"
							onClick={() => setIsDeleteOpen(true)}
						>
							<Trash2 size={16} />
						</button>
					</div>
					<DeleteScheduleModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						id={row.original.id}
					/>
				</>
			);
		},
	}),
];
