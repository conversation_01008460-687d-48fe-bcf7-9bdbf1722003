import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Schedule } from "../../service/model/schedule";
import { columns } from "./columns";

interface Props {
	schedules: Schedule[];
}

export default function Table({ schedules }: Props) {
	const table = useReactTable({
		data: schedules,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
