import { useQuery } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { scheduleOptionsById } from "../../hooks/schedule-options";
import EditSchedule from "./EditSchedule";

interface EditScheduleFormProps {
	id: string;
}

export default function EditScheduleForm({ id }: EditScheduleFormProps) {
	const svc = useService();

	const {
		data: schedule,
		isLoading,
		error,
		isSuccess,
	} = useQuery(scheduleOptionsById(svc, id));

	if (isLoading) {
		return (
			<div className="flex h-64 items-center justify-center">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="alert alert-error">
				<span>Error: {getErrorResult(error).error.message}</span>
			</div>
		);
	}

	if (isSuccess) return <EditSchedule id={id} schedule={schedule} />;
}
