import { Link } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import { Clock, FileText } from "lucide-react";
import { useState } from "react";
import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateSchedule from "../../hooks/use-create-schedule";
import { defaultValues } from "../../utils/defaultValues";
import { CreateScheduleSchema } from "../../utils/schema";
import SchedulePreview from "../SchedulePreview";
import TurnsTable from "../TurnsTable";

export default function CreateScheduleForm() {
	const [selectedTurnIndex, setSelectedTurnIndex] = useState<number | null>(
		null,
	);
	const { mutate } = useCreateSchedule();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateScheduleSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					sessionDuration: value.sessionDuration,
					breakDuration: value.breakDuration,
					turns: value.turns,
				},
				{
					onSuccess: () => {
						toast.success("Horario creado exitosamente");
						window.history.back();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<div className="grid grid-cols-1 gap-8">
					<div className="space-y-6">
						<fieldset className="fieldset">
							<div className="grid grid-cols-3 gap-4">
								<form.AppField
									name="name"
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre del Horario"
											placeholder="Nombre del Horario"
											prefixComponent={<FileText size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="sessionDuration"
									children={({ FSTextField }) => (
										<FSTextField
											label="Duración de Sesión (minutos)"
											placeholder="60"
											type="number"
											prefixComponent={<Clock size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="breakDuration"
									children={({ FSTextField }) => (
										<FSTextField
											label="Duración de Descanso (minutos)"
											placeholder="15"
											type="number"
											prefixComponent={<Clock size={16} />}
										/>
									)}
								/>
							</div>
						</fieldset>

						<TurnsTable
							form={form}
							selectedTurnIndex={selectedTurnIndex}
							onTurnSelect={setSelectedTurnIndex}
						/>

						<div className="flex gap-4">
							<form.SubscribeButton
								label="Crear Horario"
								className="btn btn-primary"
							/>
							<Link to="/admin/schedules" className="btn btn-outline">
								Cancelar
							</Link>
						</div>
					</div>

					<div className="space-y-6">
						<h3 className="font-semibold text-xl">Vista Previa del Horario</h3>
						<form.Subscribe
							selector={(state) => [
								state.values.sessionDuration,
								state.values.breakDuration,
							]}
							children={([sessionDuration, breakDuration, _]) => {
								const turns = useStore(
									form.store,
									(state) => state.values.turns,
								);
								const selectedTurn =
									selectedTurnIndex !== null ? turns[selectedTurnIndex] : null;

								if (selectedTurn)
									return (
										<SchedulePreview
											turn={selectedTurn}
											sessionDuration={sessionDuration || 60}
											breakDuration={breakDuration || 15}
										/>
									);
								return (
									<div className="card bg-base-200">
										<div className="card-body text-center">
											<p className="text-base-content/70">
												Selecciona un turno para ver la vista previa del horario
											</p>
										</div>
									</div>
								);
							}}
						/>
					</div>
				</div>
			</form.AppForm>
		</form>
	);
}
