import React from "react";
import type { TurnCreateSchema } from "../../utils/schema";

interface SchedulePreviewProps {
	turn: TurnCreateSchema;
	sessionDuration: number;
	breakDuration: number;
}

export default function SchedulePreview({
	turn,
	sessionDuration,
	breakDuration,
}: SchedulePreviewProps) {
	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const generateTimeSlots = () => {
		const startHour = Math.floor(turn.startTime / 100);
		const startMinute = turn.startTime % 100;
		const endHour = Math.floor(turn.endTime / 100);
		const endMinute = turn.endTime % 100;

		const startTimeInMinutes = startHour * 60 + startMinute;
		const endTimeInMinutes = endHour * 60 + endMinute;

		const slots = [];
		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`;
	};

	return (
		<div className="card bg-base-100">
			<div className="card-body">
				<h4 className="card-title text-lg">
					{turn.name || "Turno sin nombre"} - {formatTime(turn.startTime)} a{" "}
					{formatTime(turn.endTime)}
				</h4>

				<div className="overflow-x-auto">
					<div
						className="grid gap-1 text-xs"
						style={{
							gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
							gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`,
						}}
					>
						{/* Header row */}
						<div className="rounded bg-base-200 p-2 font-medium">Hora</div>
						{days.map((day) => (
							<div
								key={day}
								className="rounded bg-base-200 p-2 text-center font-medium"
							>
								{day}
							</div>
						))}

						{/* Time slots and content */}
						{timeSlots.map((slot, index) => (
							<React.Fragment
								key={`${slot}-${
									// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
									index
								}`}
							>
								<div className="flex items-center rounded bg-base-100 p-2 font-mono text-xs">
									{minutesToTimeString(slot.start)} -{" "}
									{minutesToTimeString(slot.end)}
								</div>
								{days.map((day) => (
									<div
										key={day}
										className="flex items-center justify-center p-1"
									>
										<div className="flex h-16 w-full items-center justify-center rounded bg-primary px-2 py-1 font-medium text-primary-content text-xs">
											{slot.label}
										</div>
									</div>
								))}
							</React.Fragment>
						))}
					</div>
				</div>

				<div className="mt-4 text-sm">
					<div className="flex gap-4">
						<div className="flex items-center gap-2">
							<div className="h-4 w-4 rounded bg-primary" />
							<span>Sesión ({sessionDuration} min)</span>
						</div>
					</div>
					<div className="mt-2 text-base-content/70 text-xs">
						Total de sesiones: {timeSlots.length}
					</div>
				</div>
			</div>
		</div>
	);
}
