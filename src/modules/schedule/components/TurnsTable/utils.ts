export const formatTime = (time: number) => {
	const hours = Math.floor(time / 100);
	const minutes = time % 100;
	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

export const parseTime = (timeString: string) => {
	const [hours, minutes] = timeString.split(":").map(Number);
	return (hours || 0) * 100 + (minutes || 0);
};

const formatTimeWithAmPm = (time: number) => {
	const hours = Math.floor(time / 100);
	const minutes = time % 100;
	const period = hours >= 12 ? "PM" : "AM";
	const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
	return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};

export const generateEndTimeOptions = (
	startTime: number,
	sessionDuration: number,
	breakDuration: number,
) => {
	const options: Array<{ value: number; label: string }> = [];
	const totalDuration = sessionDuration + breakDuration;

	const startHours = Math.floor(startTime / 100);
	const startMinutes = startTime % 100;
	let currentInMinutes = startHours * 60 + startMinutes + totalDuration;

	while (currentInMinutes < 24 * 60) {
		const currentHours = Math.floor(currentInMinutes / 60);
		const currentMinutes = currentInMinutes % 60;

		if (currentHours >= 24) break;

		const currentTime = currentHours * 100 + currentMinutes;

		options.push({
			value: currentTime,
			label: formatTimeWithAmPm(currentTime),
		});

		currentInMinutes += totalDuration;
	}

	return options;
};

export const getTotalSessions = (
	startTime: number,
	endTime: number,
	sessionDuration: number,
	breakDuration: number,
) => {
	// Convert HHMM format to total minutes
	const startHours = Math.floor(startTime / 100);
	const startMinutes = startTime % 100;
	const startInMinutes = startHours * 60 + startMinutes;

	const endHours = Math.floor(endTime / 100);
	const endMinutes = endTime % 100;
	const endInMinutes = endHours * 60 + endMinutes;

	// Calculate total available time in minutes
	const totalAvailableTime = endInMinutes - startInMinutes;

	// Calculate total duration per session (session + break)
	const totalDuration = sessionDuration + breakDuration;

	// Calculate number of complete sessions that can fit
	return Math.floor(totalAvailableTime / totalDuration);
};
