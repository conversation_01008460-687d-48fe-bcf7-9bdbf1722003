import { useStore } from "@tanstack/react-store";
import { Eye, Plus, Trash2 } from "lucide-react";
import { useEffect } from "react";
import { withForm } from "~/core/components/form/form";
import { defaultValues } from "../../utils/defaultValues";
import {
	formatTime,
	generateEndTimeOptions,
	getTotalSessions,
	parseTime,
} from "./utils";

type TurnsTableProps = {
	selectedTurnIndex: number | null;
	onTurnSelect: React.Dispatch<React.SetStateAction<number | null>>;
};
const TurnsTable = withForm({
	defaultValues,
	props: {} as TurnsTableProps,
	render: ({ form, selectedTurnIndex, onTurnSelect }) => {
		const [sessionDuration, breakDuration] = useStore(form.store, (state) => [
			state.values.sessionDuration,
			state.values.breakDuration,
		]);

		return (
			<div>
				<form.Field name="turns" mode="array">
					{(field) => {
						const turns = field.state.value;
						return (
							<div className="space-y-4">
								<div className="flex items-center justify-between gap-4">
									<span className="font-semibold">Turnos</span>
									<div className="flex justify-end">
										<button
											type="button"
											className="btn btn-primary btn-sm"
											onClick={() =>
												field.pushValue({
													name: "",
													startTime: 800,
													endTime: 1700,
												})
											}
										>
											<Plus size={16} />
											Agregar Turno
										</button>
									</div>
								</div>

								{turns.length > 0 && (
									<div className="overflow-x-auto">
										<table className="table">
											<thead>
												<tr>
													<th>Nombre</th>
													<th>Hora Inicio</th>
													<th>Hora Fin</th>
													<th>Total de sesiones</th>
													<th>Acciones</th>
												</tr>
											</thead>
											<tbody>
												{turns.map((_, index) => {
													return (
														<tr
															key={`turn-${
																// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
																index
															}`}
															className={
																selectedTurnIndex === index
																	? "bg-primary/20"
																	: ""
															}
														>
															<td>
																<form.Field name={`turns[${index}].name`}>
																	{(subField) => (
																		<input
																			type="text"
																			className="input input-sm w-full"
																			value={subField.state.value || ""}
																			onChange={(e) =>
																				subField.handleChange(e.target.value)
																			}
																			placeholder="Nombre del turno"
																		/>
																	)}
																</form.Field>
															</td>
															<td>
																<form.Field name={`turns[${index}].startTime`}>
																	{(subField) => (
																		<input
																			type="time"
																			className="input input-sm w-full"
																			value={formatTime(
																				subField.state.value || 800,
																			)}
																			onChange={(e) =>
																				subField.handleChange(
																					parseTime(e.target.value),
																				)
																			}
																		/>
																	)}
																</form.Field>
															</td>
															<td>
																<form.Subscribe
																	selector={(state) =>
																		state.values.turns[index]?.startTime
																	}
																	children={(startTime) => {
																		const endTimeOptions =
																			generateEndTimeOptions(
																				startTime || 800,
																				sessionDuration,
																				breakDuration,
																			);

																		// biome-ignore lint/correctness/useExhaustiveDependencies: depends only on startTime
																		useEffect(() => {
																			form.setFieldValue(
																				`turns[${index}].endTime`,
																				endTimeOptions[0]?.value || 1700,
																			);
																		}, [startTime]);

																		return (
																			<form.Field
																				name={`turns[${index}].endTime`}
																			>
																				{(subField) => {
																					return (
																						<select
																							className="select select-sm w-full"
																							value={
																								subField.state.value ||
																								endTimeOptions[0]?.value ||
																								1700
																							}
																							onChange={(e) =>
																								subField.handleChange(
																									Number(e.target.value),
																								)
																							}
																						>
																							{endTimeOptions.map((option) => (
																								<option
																									key={option.value}
																									value={option.value}
																								>
																									{option.label}
																								</option>
																							))}
																						</select>
																					);
																				}}
																			</form.Field>
																		);
																	}}
																/>
															</td>
															<td>
																<form.Subscribe
																	selector={(state) => [
																		state.values.turns[index]?.startTime,
																		state.values.turns[index]?.endTime,
																	]}
																	children={([startTime, endTime]) => {
																		const totalSessions = getTotalSessions(
																			startTime || 800,
																			endTime || 1700,
																			sessionDuration,
																			breakDuration,
																		);

																		return (
																			<div className="text-base-content/70 text-xs">
																				{totalSessions} sesiones
																			</div>
																		);
																	}}
																/>
															</td>
															<td>
																<div className="flex gap-2">
																	<button
																		type="button"
																		className={`btn btn-sm ${
																			selectedTurnIndex === index
																				? "btn-primary"
																				: "btn-outline"
																		}`}
																		onClick={() =>
																			onTurnSelect(
																				selectedTurnIndex === index
																					? null
																					: index,
																			)
																		}
																	>
																		<Eye size={16} />
																	</button>
																	<button
																		type="button"
																		className="btn btn-error btn-sm"
																		onClick={() => {
																			field.removeValue(index);
																			if (selectedTurnIndex === index) {
																				onTurnSelect(null);
																			} else if (
																				selectedTurnIndex !== null &&
																				selectedTurnIndex > index
																			) {
																				onTurnSelect(selectedTurnIndex - 1);
																			}
																		}}
																	>
																		<Trash2 size={16} />
																	</button>
																</div>
															</td>
														</tr>
													);
												})}
											</tbody>
										</table>
									</div>
								)}

								{turns.length === 0 && (
									<div className="card bg-base-200">
										<div className="card-body text-center">
											<p className="text-base-content/70">
												No hay turnos agregados. Haz clic en "Agregar Turno"
												para comenzar.
											</p>
										</div>
									</div>
								)}
							</div>
						);
					}}
				</form.Field>
			</div>
		);
	},
});

export default TurnsTable;
