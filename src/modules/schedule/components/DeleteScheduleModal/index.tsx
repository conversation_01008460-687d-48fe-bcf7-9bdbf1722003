import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import useDeleteSchedule from "../../hooks/use-delete-schedule";

interface DeleteScheduleModalProps {
	isOpen: boolean;
	setIsOpen: (isOpen: boolean) => void;
	id: string;
}

export default function DeleteScheduleModal({
	isOpen,
	setIsOpen,
	id,
}: DeleteScheduleModalProps) {
	const { mutate, isPending } = useDeleteSchedule();

	const handleDelete = () => {
		mutate(id, {
			onSuccess: () => {
				toast.success("Horario eliminado exitosamente");
				setIsOpen(false);
			},
			onError: (error) => {
				const { error: errorResult } = getErrorResult(error);
				toast.error(errorResult.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar Horario</h3>
				<p className="mb-4">
					¿Estás seguro de que deseas eliminar este horario? Esta acción no se
					puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? (
							<span className="loading loading-spinner loading-sm" />
						) : (
							"Eliminar"
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
