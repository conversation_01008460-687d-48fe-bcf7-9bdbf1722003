import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type CreateSchedule, Schedule, Turn } from "../service/model/schedule";
import { scheduleOptions } from "./schedule-options";

export default function useCreateSchedule() {
	const service = useService();
	const { schedule } = service;
	const queryClient = useQueryClient();
	const queryKey = scheduleOptions(service).queryKey;

	return useMutation({
		mutationFn: (newSchedule: CreateSchedule) =>
			AppRuntime.runPromise(schedule.create(newSchedule)),
		onMutate: async (newSchedule) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSchedules = queryClient.getQueryData(queryKey);

			if (previousSchedules) {
				queryClient.setQueryData(
					queryKey,
					create(previousSchedules, (draft) => {
						draft.push(
							Schedule.make({
								id: "new",
								...newSchedule,
								turns: newSchedule.turns.map((turn) =>
									Turn.make({
										id: "new",
										...turn,
										createdAt: null,
										updatedAt: null,
										deletedAt: null,
									}),
								),
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							}),
						);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					Schedule.make({
						id: "new",
						...newSchedule,
						turns: newSchedule.turns.map((turn) =>
							Turn.make({
								id: "new",
								...turn,
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							}),
						),
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
				]);
			}

			return { previousSchedules };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSchedules);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
