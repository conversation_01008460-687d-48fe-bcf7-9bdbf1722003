import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Schedule } from "../service/model/schedule";
import { scheduleOptions } from "./schedule-options";

export default function useDeleteSchedule() {
	const service = useService();
	const { schedule } = service;
	const queryClient = useQueryClient();
	const queryKey = scheduleOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-schedule"],
		mutationFn: (scheduleId: string) =>
			AppRuntime.runPromise(schedule.delete(scheduleId)),
		onMutate: async (scheduleId) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSchedules = queryClient.getQueryData(queryKey);

			if (previousSchedules) {
				queryClient.setQueryData(
					queryKey,
					create(previousSchedules, (draft) => {
						const index = draft.findIndex((s) => s.id === scheduleId);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousSchedules };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSchedules);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
