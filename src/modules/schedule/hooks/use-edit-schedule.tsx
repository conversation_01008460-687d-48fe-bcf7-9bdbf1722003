import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { Schedule, Turn, type UpdateSchedule } from "../service/model/schedule";
import { scheduleOptions } from "./schedule-options";

export default function useEditSchedule() {
	const service = useService();
	const { schedule } = service;
	const queryClient = useQueryClient();
	const queryKey = scheduleOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedSchedule: UpdateSchedule) =>
			AppRuntime.runPromise(schedule.update(updatedSchedule)),
		onMutate: async (updatedSchedule) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSchedules = queryClient.getQueryData(queryKey);

			if (previousSchedules) {
				queryClient.setQueryData(
					queryKey,
					create(previousSchedules, (draft) => {
						const index = draft.findIndex((s) => s.id === updatedSchedule.id);
						if (index !== -1) {
							draft[index] = Schedule.make({
								...updatedSchedule,
								id: updatedSchedule.id,
								turns: updatedSchedule.turns.map((turn) =>
									Turn.make({
										...turn,
										id: "new",
										createdAt: null,
										updatedAt: null,
										deletedAt: null,
									}),
								),
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							});
						}
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					Schedule.make({
						...updatedSchedule,
						id: updatedSchedule.id,
						turns: updatedSchedule.turns.map((turn) =>
							Turn.make({
								...turn,
								id: "new",
								createdAt: null,
								updatedAt: null,
								deletedAt: null,
							}),
						),
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
				]);
			}

			return { previousSchedules };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSchedules);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
