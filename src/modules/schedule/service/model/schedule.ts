import { Schema } from "effect";

export const Turn = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	startTime: Schema.Number,
	endTime: Schema.Number,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Turn = typeof Turn.Type;

export const Schedule = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	sessionDuration: Schema.Number,
	breakDuration: Schema.Number,
	turns: Schema.mutable(Schema.Array(Turn)),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Schedule = typeof Schedule.Type;

export const TurnCreate = Schema.Struct({
	name: Schema.String,
	startTime: Schema.Number,
	endTime: Schema.Number,
});
export type TurnCreate = typeof TurnCreate.Type;

export const TurnUpdate = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	startTime: Schema.Number,
	endTime: Schema.Number,
});
export type TurnUpdate = typeof TurnUpdate.Type;

export const CreateSchedule = Schema.Struct({
	name: Schema.String,
	sessionDuration: Schema.Number,
	breakDuration: Schema.Number,
	turns: Schema.mutable(Schema.Array(TurnCreate)),
});
export type CreateSchedule = typeof CreateSchedule.Type;

export const UpdateSchedule = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	sessionDuration: Schema.Number,
	breakDuration: Schema.Number,
	turns: Schema.mutable(Schema.Array(TurnUpdate)),
});
export type UpdateSchedule = typeof UpdateSchedule.Type;
