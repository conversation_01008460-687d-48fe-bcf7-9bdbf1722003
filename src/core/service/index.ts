import type { LoginCredentials } from "~/auth/service/model/auth";
import { AuthUsecase } from "~/auth/service/model/usecase";
import type {
	CreateClient,
	UpdateClient,
} from "~/modules/client/service/model/client";
import type {
	CreatePublicClientLink,
	UpdatePublicClientLink,
} from "~/modules/client/service/model/publicClientLink";
import { ClientUsecase } from "~/modules/client/service/model/usecase";
import type {
	CreateSchedule,
	UpdateSchedule,
} from "~/modules/schedule/service/model/schedule";
import { ScheduleUsecase } from "~/modules/schedule/service/model/usecase";
import type {
	CreateManySession,
	CreateSession,
	DeleteManySession,
	UpdateSession,
} from "~/modules/session/service/model/session";
import { SessionUsecase } from "~/modules/session/service/model/usecase";
import { WorkerUsecase } from "~/worker/service/model/usecase";
import type { <PERSON><PERSON><PERSON>or<PERSON>, UpdateWorker } from "~/worker/service/model/worker";

const authService = {
	login: (credentials: LoginCredentials) => AuthUsecase.login(credentials),
	logout: () => AuthUsecase.logout(),
	getSession: () => AuthUsecase.getSession(),
};

const clientService = {
	create: (client: CreateClient) => ClientUsecase.create(client),
	getAll: () => ClientUsecase.getAll(),
	getById: (id: string) => ClientUsecase.getById(id),
	update: (client: UpdateClient) => ClientUsecase.update(client),
	delete: (id: string) => ClientUsecase.delete(id),
	generatePublicLink: () => ClientUsecase.generatePublicLink(),
	createPublicLink: (link: CreatePublicClientLink) =>
		ClientUsecase.createPublicLink(link),
	updatePublicLink: (link: UpdatePublicClientLink) =>
		ClientUsecase.updatePublicLink(link),
	getClientLink: (url: string) => ClientUsecase.getClientLink(url),
	deletePublicLink: (id: string) => ClientUsecase.deletePublicLink(id),
};

const workerService = {
	create: (worker: CreateWorker) => WorkerUsecase.create(worker),
	getAll: () => WorkerUsecase.getAll(),
	getById: (id: string) => WorkerUsecase.getById(id),
	update: (worker: UpdateWorker) => WorkerUsecase.update(worker),
	delete: (id: string) => WorkerUsecase.delete(id),
};

const scheduleService = {
	create: (schedule: CreateSchedule) => ScheduleUsecase.create(schedule),
	getAll: () => ScheduleUsecase.getAll(),
	getById: (id: string) => ScheduleUsecase.getById(id),
	update: (schedule: UpdateSchedule) => ScheduleUsecase.update(schedule),
	delete: (id: string) => ScheduleUsecase.delete(id),
};

const sessionService = {
	create: (session: CreateSession) => SessionUsecase.create(session),
	getAll: () => SessionUsecase.getAll(),
	getById: (id: string) => SessionUsecase.getById(id),
	getByClientAndTurn: (clientId: string, turnId: string) =>
		SessionUsecase.getByClientAndTurn(clientId, turnId),
	getByWorkerAndTurn: (workerId: string, turnId: string) =>
		SessionUsecase.getByWorkerAndTurn(workerId, turnId),
	update: (session: UpdateSession) => SessionUsecase.update(session),
	delete: (id: string) => SessionUsecase.delete(id),
	createMany: (sessions: CreateManySession) =>
		SessionUsecase.createMany(sessions),
	deleteMany: (request: DeleteManySession) =>
		SessionUsecase.deleteMany(request),
};

export const serviceRegistry = {
	auth: authService,
	client: clientService,
	worker: workerService,
	schedule: scheduleService,
	session: sessionService,
};

export type serviceRegistry = typeof serviceRegistry;
