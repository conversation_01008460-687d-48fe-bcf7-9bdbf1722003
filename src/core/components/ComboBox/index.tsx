import type { ClassValue } from "clsx";
import { useCombobox, useMultipleSelection } from "downshift";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useMutative } from "use-mutative";
import { cn } from "~/core/utils/classes";

// Types
export interface Option {
	value: number | string;
	label: string;
}

type Size = "xs" | "sm" | "md" | "lg" | "xl";

interface BaseComboBoxProps {
	label?: string;
	isLoading?: boolean;
	options: Option[];
	placeholder?: string;
	className?: ClassValue;
	hideReset?: boolean;
	size?: Size;
}

interface MultipleComboBoxProps extends BaseComboBoxProps {
	isMultiple: true;
	defaultSelected?: Option[];
	value?: Option[];
	onChange: (selectedItems: Option[]) => void;
}

interface SingleComboBoxProps extends BaseComboBoxProps {
	isMultiple?: false;
	defaultSelected?: Option | null;
	value?: Option | null;
	onChange: (selectedItem: Option | null) => void;
}

type ComboBoxProps = MultipleComboBoxProps | SingleComboBoxProps;

// Main ComboBox component
export default function ComboBox(props: ComboBoxProps) {
	if (props.isMultiple) {
		return <MultipleComboBox {...props} />;
	}
	return <SingleComboBox {...props} />;
}

// Multiple selection ComboBox
function MultipleComboBox({
	value = [],
	isLoading = false,
	options,
	defaultSelected = [],
	onChange,
	placeholder = "Seleccionar...",
	className,
	hideReset = false,
	size = "md",
}: MultipleComboBoxProps) {
	const [selectedItems, setSelectedItems] = useState<Option[]>(defaultSelected);

	useEffect(() => {
		setSelectedItems(defaultSelected);
	}, [defaultSelected]);

	// Filter out already selected items
	const availableItems = useMemo(
		() =>
			options.filter(
				(option) =>
					!selectedItems.some((selected) => selected.value === option.value),
			),
		[selectedItems, options],
	);

	// Handle selection changes
	const handleSelectionChange = useCallback(
		(newSelectedItems: Option[]) => {
			setSelectedItems(newSelectedItems);
			onChange(newSelectedItems);
		},
		[onChange],
	);

	// Handle item removal
	const handleRemoveItem = useCallback(
		(itemToRemove: Option) => {
			const updatedItems = selectedItems.filter(
				(item) => item.value !== itemToRemove.value,
			);
			handleSelectionChange(updatedItems);
		},
		[selectedItems, handleSelectionChange],
	);

	// Handle clear all
	const handleClearAll = useCallback(() => {
		handleSelectionChange([]);
	}, [handleSelectionChange]);

	const { getSelectedItemProps, getDropdownProps, removeSelectedItem } =
		useMultipleSelection({
			selectedItems,
			defaultSelectedItems: defaultSelected,
			onStateChange({ selectedItems: newSelectedItems, type }) {
				switch (type) {
					case useMultipleSelection.stateChangeTypes
						.SelectedItemKeyDownBackspace:
					case useMultipleSelection.stateChangeTypes.SelectedItemKeyDownDelete:
					case useMultipleSelection.stateChangeTypes.DropdownKeyDownBackspace:
					case useMultipleSelection.stateChangeTypes.FunctionRemoveSelectedItem:
						if (newSelectedItems) {
							handleSelectionChange(newSelectedItems);
						}
						break;
					default:
						break;
				}
			},
		});

	const {
		isOpen,
		getToggleButtonProps,
		getMenuProps,
		getInputProps,
		highlightedIndex,
		getItemProps,
		setInputValue,
	} = useCombobox({
		items: availableItems,
		itemToString: (item) => item?.label ?? "",
		defaultHighlightedIndex: 0,
		selectedItem: null,
		stateReducer(_, actionAndChanges) {
			const { changes, type } = actionAndChanges;
			switch (type) {
				case useCombobox.stateChangeTypes.InputKeyDownEnter:
				case useCombobox.stateChangeTypes.ItemClick:
					return {
						...changes,
						isOpen: true,
						highlightedIndex: 0,
					};
				default:
					return changes;
			}
		},
		onStateChange({ type, selectedItem: newSelectedItem }) {
			switch (type) {
				case useCombobox.stateChangeTypes.InputKeyDownEnter:
				case useCombobox.stateChangeTypes.ItemClick:
				case useCombobox.stateChangeTypes.InputBlur:
					if (newSelectedItem) {
						const updatedSelectedItems = [...selectedItems, newSelectedItem];
						handleSelectionChange(updatedSelectedItems);
						setInputValue("");
					}
					break;
				default:
					break;
			}
		},
	});

	const renderSelectedItem = useCallback(
		(selectedItem: Option, index: number) => (
			<div
				key={`selected-item-${selectedItem.value}-${index}`}
				className="badge badge-primary pr-0"
			>
				<div
					className="rounded-l-md"
					{...getSelectedItemProps({ selectedItem, index })}
				>
					{selectedItem.label}
				</div>
				<button
					type="button"
					className="btn btn-xs btn-circle btn-error btn-ghost"
					onClick={(e) => {
						e.stopPropagation();
						handleRemoveItem(selectedItem);
					}}
				>
					<X size={16} />
				</button>
			</div>
		),
		[getSelectedItemProps, handleRemoveItem],
	);

	const renderMenuItem = useCallback(
		(item: Option, index: number) => (
			<li
				key={`${item.value}-${index}`}
				className={cn(
					"flex cursor-pointer flex-col px-3 py-2 shadow-sm",
					highlightedIndex === index && "bg-base-content text-base-200",
				)}
				{...getItemProps({ item, index })}
			>
				<span>{item.label}</span>
			</li>
		),
		[highlightedIndex, getItemProps],
	);

	return (
		<div className={cn("dropdown", className)}>
			<div className="join input h-fit w-full items-center rounded-md p-1">
				{isLoading ? (
					<span className="loading loading-dots join-item loading-sm" />
				) : (
					<div className="inline-flex w-full flex-wrap items-center gap-1">
						{selectedItems.map(renderSelectedItem)}
						<input
							placeholder={placeholder}
							className="input input-ghost flex-1 focus:outline-none"
							{...getInputProps(getDropdownProps({ preventKeyAction: isOpen }))}
						/>
					</div>
				)}
				<button
					className="btn btn-sm join-item btn-ghost btn-circle"
					type="button"
					onClick={handleClearAll}
					disabled={selectedItems.length === 0}
				>
					<X className="h-5 w-5" />
				</button>
				<button
					aria-label="toggle menu"
					className="btn btn-sm join-item btn-ghost btn-circle"
					type="button"
					{...getToggleButtonProps()}
				>
					{isOpen ? (
						<ArrowUp className="h-5 w-5" />
					) : (
						<ArrowDown className="h-5 w-5" />
					)}
				</button>
			</div>
			<ul
				className={cn(
					"dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",
					(!isOpen || !availableItems.length) && "hidden",
				)}
				{...getMenuProps()}
			>
				{isOpen && availableItems.map(renderMenuItem)}
			</ul>
		</div>
	);
}

// Single selection ComboBox
function SingleComboBox({
	value = null,
	isLoading = false,
	options,
	defaultSelected = null,
	onChange,
	placeholder = "Seleccionar...",
	className,
	size = "md",
	label,
	hideReset = false,
}: SingleComboBoxProps) {
	const [items, setItems] = useMutative<Option[]>(options);
	const [inputValue, setInputValue] = useState(
		defaultSelected?.label || value?.label || "",
	);

	// Filter items based on input
	const filterItems = useCallback(
		(searchValue: string) => {
			setItems((draft) => {
				// Reset to original options
				for (let i = 0; i < options.length; i++) {
					// @ts-ignore
					draft[i] = options[i];
				}
				draft.length = options.length;

				// Filter based on search
				if (searchValue) {
					let keepIndex = 0;
					const lowerSearchValue = searchValue.toLowerCase();

					for (let i = 0; i < draft.length; i++) {
						const item = draft[i];
						if (item?.label.toLowerCase().includes(lowerSearchValue)) {
							draft[keepIndex] = item;
							keepIndex++;
						}
					}
					draft.length = keepIndex;
				}
			});
		},
		[options, setItems],
	);

	const {
		isOpen,
		getToggleButtonProps,
		getMenuProps,
		getInputProps,
		highlightedIndex,
		getItemProps,
		reset,
		getLabelProps,
	} = useCombobox({
		items,
		defaultSelectedItem: defaultSelected || value,
		inputValue,
		itemToString: (item) => item?.label ?? "",
		onInputValueChange({ inputValue: newInputValue }) {
			setInputValue(newInputValue || "");
			filterItems(newInputValue || "");
		},
		onSelectedItemChange: ({ selectedItem }) => {
			onChange(selectedItem);
		},
		onIsOpenChange: ({ isOpen: newIsOpen, selectedItem }) => {
			if (!newIsOpen) {
				setInputValue(selectedItem?.label || "");
			}
		},
	});

	// Reset items when options change
	useEffect(() => {
		setItems(options);
	}, [options, setItems]);

	// Reset when value becomes null
	useEffect(() => {
		if (value === null) {
			reset();
		}
	}, [value, reset]);

	const renderMenuItem = useCallback(
		(item: Option, index: number) => (
			<li
				key={`${item.value}-${index}`}
				className={cn(
					"flex cursor-pointer flex-col px-3 py-2 shadow-sm",
					highlightedIndex === index && "bg-base-content text-base-200",
					value?.value === item.value && "font-bold",
				)}
				{...getItemProps({ item, index })}
			>
				<span>{item.label}</span>
			</li>
		),
		[highlightedIndex, value, getItemProps],
	);

	return (
		<div className={cn("dropdown", className)}>
			<label htmlFor="combo" className="label" {...getLabelProps()}>
				{label}
			</label>
			<div className="join h-fit w-full items-center bg-base-100">
				{isLoading ? (
					<span className="loading loading-dots join-item loading-sm" />
				) : (
					<input
						placeholder={placeholder}
						className="input input-sm input-bordered join-item w-full"
						{...getInputProps()}
					/>
				)}
				{!hideReset && (
					<button
						className="btn btn-sm btn-soft join-item"
						type="button"
						onClick={reset}
						disabled={!value}
					>
						<X className="h-5 w-5" />
					</button>
				)}
				<button
					aria-label="toggle menu"
					className="btn btn-sm btn-soft join-item"
					type="button"
					{...getToggleButtonProps()}
				>
					{isOpen ? (
						<ArrowUp className="h-5 w-5" />
					) : (
						<ArrowDown className="h-5 w-5" />
					)}
				</button>
			</div>
			<ul
				className={cn(
					"dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",
					(!isOpen || !items.length) && "hidden",
				)}
				{...getMenuProps()}
			>
				{isOpen && items.map(renderMenuItem)}
			</ul>
		</div>
	);
}
