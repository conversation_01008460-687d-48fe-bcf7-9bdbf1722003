import { useTheme } from "~/config/context/themeProvider";
import { cn } from "../utils/classes";
import { themes } from "../utils/themes";

interface Props {
	className?: string;
}
export default function ThemeSwitcher({ className }: Props) {
	const { setTheme, theme } = useTheme();

	return (
		<select
			defaultValue="Elige un tema"
			className={cn("select", className)}
			value={theme}
			onChange={(e) => setTheme(e.target.value)}
		>
			{themes.map((_theme) => (
				<option key={_theme} value={_theme}>
					{_theme}
				</option>
			))}
		</select>
	);
}
