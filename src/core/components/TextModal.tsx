import { useState } from "react";
import { cn } from "~/core/utils/classes";
import CloseModal from "./CloseModal";

interface Props {
	open?: boolean;
	title?: string;
	text?: string;
}
export default function TextModal({ open = false, title, text }: Props) {
	const [_open, setOpen] = useState(open);

	return (
		<div className={cn("modal", open && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setOpen(false)} />
				<h3 className="font-bold text-lg">{title}</h3>
				<p>{text}</p>
			</div>
		</div>
	);
}
