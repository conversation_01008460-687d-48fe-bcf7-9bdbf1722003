import type { HTMLInputTypeAttribute } from "react";
import { useFieldContext } from "./form";

export function FSTextField({
  label,
  placeholder,
  type = "text",
  prefixComponent,
  suffixComponent,
}: {
  label: string;
  placeholder: string;
  type?: HTMLInputTypeAttribute;
  prefixComponent?: React.ReactNode;
  suffixComponent?: React.ReactNode;
}) {
  const field = useFieldContext<string | number>();
  return (
    <fieldset className="fieldset">
      <legend className="fieldset-legend">{label}</legend>
      <div className="input w-full">
        {prefixComponent && prefixComponent}
        <input
          type={type}
          className="grow"
          placeholder={placeholder}
          value={
            type === "number"
              ? field.state.value?.toString()
              : field.state.value
          }
          onChange={(e) =>
            field.handleChange(
              // @ts-ignore
              type === "number" ? Number(e.target.value) : e.target.value,
            )
          }
        />
        {suffixComponent && suffixComponent}
      </div>
      {field.state.meta.isTouched && field.state.meta.errors.length
        ? field.state.meta.errors.flatMap((error) => (
            <p key={error.message} className="fieldset-label text-error">
              {error.message}
            </p>
          ))
        : null}
    </fieldset>
  );
}
