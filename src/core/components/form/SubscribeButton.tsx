import { useFormContext } from "./form";

export function SubscribeButton({
  label,
  className = "btn btn-neutral",
}: {
  label: string;
  className?: React.HTMLAttributes<HTMLButtonElement>["className"];
}) {
  const { Subscribe } = useFormContext();
  return (
    <Subscribe
      selector={(state) => ({
        isSubmitting: state.isSubmitting,
        canSubmit: state.canSubmit,
      })}
    >
      {({ isSubmitting, canSubmit }) => (
        <button disabled={isSubmitting || !canSubmit} className={className}>
          {isSubmitting && <span className="loading loading-spinner"></span>}
          {label}
        </button>
      )}
    </Subscribe>
  );
}
