import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import CreateWorkerModal from "~/worker/components/CreateWorkerModal";
import WorkerTable from "~/worker/components/WorkerTable";

export const Route = createFileRoute("/_authed/admin/workers/")({
	component: RouteComponent,
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Nuevo trabajador
							</button>
						</div>
						<WorkerTable />
					</div>
				</div>
			</div>
			<CreateWorkerModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
