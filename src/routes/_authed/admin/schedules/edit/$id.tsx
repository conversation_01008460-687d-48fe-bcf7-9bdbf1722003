import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import EditScheduleForm from "~/modules/schedule/components/EditScheduleForm";

export const Route = createFileRoute("/_authed/admin/schedules/edit/$id")({
	component: RouteComponent,
});

function RouteComponent() {
	const { id } = Route.useParams();

	return (
		<div className="container mx-auto max-w-6xl">
			<div className="mb-6">
				<Link to="/admin/schedules" className="btn btn-ghost">
					<ArrowLeft size={16} />
					Volver a Horarios
				</Link>
			</div>

			<div className="card bg-base-300">
				<div className="card-body">
					<h2 className="card-title mb-6 text-2xl">Editar Horario</h2>
					<EditScheduleForm id={id} />
				</div>
			</div>
		</div>
	);
}
