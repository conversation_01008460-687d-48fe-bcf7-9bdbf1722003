@import "tailwindcss" source("../../");

@plugin "daisyui" {
  themes: all;
}

* {
  font-family: "Poppins", sans-serif;
}

:root {
  --toastify-color-light: var(--color-base-100);
  --toastify-color-dark: var(--color-neutral);
  --toastify-color-info: var(--color-info);
  --toastify-color-success: var(--color-success);
  --toastify-color-warning: var(--color-warning);
  --toastify-color-error: var(--color-error);
  --toastify-color-transparent: rgba(255, 255, 255, 0.7);

  --toastify-icon-color-info: var(--color-info);
  --toastify-icon-color-success: var(--color-success);
  --toastify-icon-color-warning: var(--color-warning);
  --toastify-icon-color-error: var(--color-error);

  --toastify-container-width: fit-content;
  --toastify-toast-width: 320px;
  --toastify-toast-offset: 16px;
  --toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));
  --toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));
  --toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));
  --toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));
  --toastify-toast-background: var(--color-base-100);
  --toastify-toast-padding: 14px;
  --toastify-toast-min-height: 64px;
  --toastify-toast-max-height: 800px;
  --toastify-toast-bd-radius: 6px;
  --toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --toastify-font-family: sans-serif;
  --toastify-z-index: 9999;
  --toastify-text-color-light: var(--color-base-content);
  --toastify-text-color-dark: var(--color-neutral-content);

  /* Used only for colored theme */
  --toastify-text-color-info: var(--color-info-content);
  --toastify-text-color-success: var(--color-success-content);
  --toastify-text-color-warning: var(--color-warning-content);
  --toastify-text-color-error: var(--color-error-content);

  --toastify-spinner-color: var(--color-base-content);
  --toastify-spinner-color-empty-area: var(--color-base-200);
  --toastify-color-progress-light: linear-gradient(to right, var(--color-success), var(--color-info), var(--color-primary), var(--color-accent), var(--color-secondary), var(--color-error));
  --toastify-color-progress-dark: var(--color-primary);
  --toastify-color-progress-info: var(--color-info);
  --toastify-color-progress-success: var(--color-success);
  --toastify-color-progress-warning: var(--color-warning);
  --toastify-color-progress-error: var(--color-error);
  --toastify-color-progress-bgo: 0.2;
}

.Toastify__close-button {
  color: var(--toastify-text-color-dark);
}

.poppins-thin {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.poppins-extralight {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: normal;
}

.poppins-light {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.poppins-regular {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.poppins-medium {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.poppins-semibold {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: normal;
}

.poppins-bold {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.poppins-extrabold {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: normal;
}

.poppins-black {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: normal;
}

.poppins-thin-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: italic;
}

.poppins-extralight-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: italic;
}

.poppins-light-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.poppins-regular-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.poppins-medium-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.poppins-semibold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: italic;
}

.poppins-bold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: italic;
}

.poppins-extrabold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: italic;
}

.poppins-black-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: italic;
}