import { createContext, useContext } from "react";
import type { serviceRegistry } from "~/core/service";

type ServiceRegistry = serviceRegistry;

const ServiceContext = createContext<ServiceRegistry | undefined>(undefined);

export const ServiceProvider = ({
	children,
	service,
}: { children: React.ReactNode; service: ServiceRegistry }) => (
	<ServiceContext.Provider value={service}>{children}</ServiceContext.Provider>
);

export const useService = () => {
	const service = useContext(ServiceContext);
	if (!service) throw new Error("No service found");
	return service;
};
