// app/router.tsx
import { createRouter as createTanStackRouter } from '@tanstack/react-router'
import { routeTree } from './routeTree.gen'
import { QueryClient } from '@tanstack/react-query';
import { routerWithQueryClient } from '@tanstack/react-router-with-query';

export function createRouter() {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				retry: false,
			},
		},
	});

	const router = routerWithQueryClient(
		// @ts-ignore
		createTanStackRouter({
			routeTree,
			defaultPendingComponent: () => <div>Cargando...</div>,
			context: { queryClient },
			defaultPreload: "intent",
			defaultPreloadStaleTime: 0,
			scrollRestoration: true,
		}),
		queryClient,
	);

	return router;
}

export const router = createRouter();


declare module '@tanstack/react-router' {
  interface Register {
    router: ReturnType<typeof createRouter>
  }
}