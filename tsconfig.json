{
  "compilerOptions": {
    // Environment setup & latest features
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ],
    "target": "ESNext",
    "module": "ESNext",
    "moduleDetection": "force",
    "jsx": "react-jsx",
    "allowJs": true,
    // Bundler mode
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "noEmit": true,
    // Best practices
    "strict": true,
    "skipLibCheck": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    // Some stricter flags (disabled by default)
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noPropertyAccessFromIndexSignature": false,
    "strictNullChecks": true,
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "./src/*"
      ],
      "~/auth/*": [
        "./src/modules/auth/*"
      ],
      "~/user/*": [
        "./src/modules/user/*"
      ],
      "~/person/*": [
        "./src/modules/person/*"
      ],
      "~/worker/*": [
        "./src/modules/worker/*"
      ],
      "~/client/*": [
        "./src/modules/client/*"
      ],
      "~/schedule/*": [
        "./src/modules/schedule/*"
      ]
    },
  }
}